/**
 * <PERSON><PERSON><PERSON><PERSON> de navegación basada en hash URLs para iPRA
 * Implementa la navegación mediante hash URLs y captura la tecla atrás del navegador
 */

// Mapa de hash a pantallas (inicializado con valores básicos)
var hashToScreenMap = {
    'menu': 'main-menu',
    'login': 'login',
    'tablero': 'dashboard'
};

// Parámetros adicionales para cada tipo de pantalla
var screenParams = {};

/**
 * Registra una nueva entidad en el sistema de navegación por hash
 * @param {string} hashName - El nombre del hash en la URL (ej: 'empresas')
 * @param {string} entityType - El tipo de entidad (ej: 'companies')
 */
function registerEntityHash(hashName, entityType) {
    // Registrar en el mapa de hash a pantallas
    hashToScreenMap[hashName] = 'entity-management';

    // Registrar los parámetros para esta entidad
    screenParams[hashName] = {
        entityType: entityType
    };

    console.log(`Entidad registrada en navegación por hash: ${hashName} -> ${entityType}`);
}

/**
 * Inicializa la navegación basada en hash
 */
function initHashNavigation() {
    // Registrar las entidades existentes
    if (app.entityManager && app.entityManager.entityHandlers) {
        // Registrar las entidades ya existentes
        Object.keys(app.entityManager.entityHandlers).forEach(entityType => {
            // Convertir el tipo de entidad a un nombre de hash adecuado
            let hashName;
            switch (entityType) {
                case 'companies':
                    hashName = 'empresas';
                    break;
                case 'usuarios':
                    hashName = 'usuarios';
                    break;
                default:
                    // Para otros tipos, usar el mismo nombre
                    hashName = entityType;
            }

            // Registrar en el sistema de navegación por hash
            registerEntityHash(hashName, entityType);
        });
    }

    // Solo capturar el evento popstate (botón atrás/adelante del navegador)
    // No necesitamos hashchange porque handlePopState se encarga de toda la navegación
    window.addEventListener('popstate', handlePopState);
}



/**
 * Navega a la pantalla correspondiente según el hash
 * @param {string} hashInput - El hash de la URL sin el # (puede ser el hash completo o solo el hash base)
 * @param {Array} additionalParams - Parámetros adicionales extraídos del hash (opcional)
 */
function navigateToHash(hashInput, additionalParams = []) {
    // Si el usuario no está autenticado, solo permitir la navegación a login
    if (!authManager.isAuthenticated() && hashInput !== 'login') {
        console.log('Usuario no autenticado, redirigiendo a login');
        navigateTo('login');
        return;
    }

    // Procesar el hash para extraer la base y los parámetros
    let baseHash, hashParams;

    if (Array.isArray(additionalParams) && additionalParams.length > 0) {
        // Si se proporcionan parámetros adicionales, hashInput es el hash base
        baseHash = hashInput;
        hashParams = additionalParams;
    } else {
        // Si no hay parámetros adicionales, verificar si hashInput contiene parámetros
        const parts = hashInput.split('/');
        baseHash = parts[0];
        hashParams = parts.slice(1);
    }

    console.log(`navigateToHash: Base=${baseHash}, Params=${hashParams.join('/')}`);

    // Obtener la pantalla correspondiente al hash base
    const screen = hashToScreenMap[baseHash];

    if (screen) {
        // Obtener parámetros adicionales si existen
        const params = { ...screenParams[baseHash] } || {};

        // Procesar parámetros específicos según el hash
        if (baseHash === 'usuarios' && hashParams.length > 0) {
            // El primer parámetro después de 'usuarios/' es el ID de la empresa
            const companyId = parseInt(hashParams[0], 10);
            if (!isNaN(companyId)) {
                // Forzar el ID de empresa como número
                params.companyId = Number(companyId);

                // Logs detallados para depuración
                console.log(`Hash: Usando ID de empresa específico: ${companyId}`);

                // El segundo parámetro es el nombre de la empresa (opcional, no se usa funcionalmente)
                if (hashParams.length > 1) {
                    // Guardar el nombre de la empresa para mostrar en la interfaz
                    params.companyName = hashParams[1];
                    console.log(`Hash: Nombre de empresa en URL: ${hashParams[1]}`);
                }
            }
        } else if (baseHash === 'tableros' && hashParams.length > 0) {
            // El primer parámetro después de 'tableros/' es el ID de la empresa
            const companyId = parseInt(hashParams[0], 10);
            if (!isNaN(companyId)) {
                // Forzar el ID de empresa como número
                params.companyId = Number(companyId);

                // Logs detallados para depuración
                console.log(`Hash: Usando ID de empresa específico para tableros: ${companyId}`);

                // El segundo parámetro es el nombre de la empresa (opcional, no se usa funcionalmente)
                if (hashParams.length > 1) {
                    // Guardar el nombre de la empresa para mostrar en la interfaz
                    params.companyName = hashParams[1];
                    console.log(`Hash: Nombre de empresa en URL para tableros: ${hashParams[1]}`);
                }
            }
        } else if (baseHash === 'tablero' && hashParams.length > 0) {
            // Acceso directo a tablero por ID: #tablero/idTablero
            const dashboardId = parseInt(hashParams[0], 10);
            if (!isNaN(dashboardId)) {
                console.log(`Hash: Acceso directo a tablero ID: ${dashboardId}`);

                // Buscar el tablero por ID para obtener la empresa
                dbManager.findDashboardById(dashboardId)
                    .then(result => {
                        if (result && result.dashboard && result.company) {
                            console.log(`Hash: Tablero encontrado, empresa: ${result.company.nombre} (ID: ${result.company.id})`);

                            // Establecer la empresa del usuario si es necesario
                            if (authManager.companyId !== result.company.id) {
                                // Solo permitir si el usuario es admin de empresa 1 o pertenece a esa empresa
                                if (authManager.user.tipo === 'admin' && authManager.companyId === 1) {
                                    console.log('Hash: Usuario admin accediendo a tablero de otra empresa');
                                } else if (authManager.companyId === result.company.id) {
                                    console.log('Hash: Usuario accediendo a tablero de su empresa');
                                } else {
                                    console.warn('Hash: Usuario sin permisos para acceder a este tablero');
                                    navigateTo('main-menu');
                                    return;
                                }
                            }

                            // Cargar los datos de la empresa y abrir el tablero
                            dbManager.getCompanyData(result.company.id)
                                .then(companyData => {
                                    if (companyData) {
                                        // Establecer el contexto de empresa temporalmente
                                        const originalCompanyId = authManager.companyId;
                                        authManager.companyId = result.company.id;

                                        // Establecer el contexto en dashboardManager también
                                        window.dashboardManager.companyId = result.company.id;

                                        console.log(`Hash: Cambiando contexto de empresa ${originalCompanyId} a ${result.company.id} para acceso a tablero`);

                                        // Cargar el dashboard manager con los datos de la empresa
                                        window.dashboardManager.loadCompanyData(companyData)
                                            .then(() => {
                                                // Configurar el entityManager para tableros con el contexto de empresa correcto
                                                // Esto asegura que cuando se creen nuevos tableros, se asocien a la empresa correcta
                                                if (app.entityManager) {
                                                    // Buscar o crear un entityManager para tableros
                                                    let tablerosEntityManager = app.entityManagers.find(em => em.entityType === 'tableros');
                                                    if (!tablerosEntityManager) {
                                                        // Si no existe, crear uno nuevo
                                                        tablerosEntityManager = new EntityManager();
                                                        app.entityManagers.push(tablerosEntityManager);
                                                    }

                                                    // Configurar los parámetros de empresa
                                                    tablerosEntityManager.entityType = 'tableros';
                                                    tablerosEntityManager.params = { companyId: result.company.id };

                                                    console.log(`Hash: Configurado entityManager para tableros con empresa ${result.company.id}`);
                                                }

                                                // Buscar el tablero específico
                                                const dashboard = companyData.tableros.find(d => d.id === dashboardId);
                                                if (dashboard) {
                                                    // Abrir el tablero directamente
                                                    window.dashboardManager.useDashboard(dashboard);
                                                } else {
                                                    console.error('Hash: Tablero no encontrado en los datos de la empresa');
                                                    navigateTo('main-menu');
                                                }
                                            })
                                            .catch(error => {
                                                console.error('Hash: Error al cargar datos de empresa:', error);
                                                navigateTo('main-menu');
                                            });
                                    } else {
                                        console.error('Hash: No se pudieron cargar los datos de la empresa');
                                        navigateTo('main-menu');
                                    }
                                })
                                .catch(error => {
                                    console.error('Hash: Error al obtener datos de empresa:', error);
                                    navigateTo('main-menu');
                                });
                        } else {
                            console.error(`Hash: Tablero con ID ${dashboardId} no encontrado`);
                            navigateTo('main-menu');
                        }
                    })
                    .catch(error => {
                        console.error('Hash: Error al buscar tablero:', error);
                        navigateTo('main-menu');
                    });

                // Salir temprano ya que el procesamiento es asíncrono
                return;
            }
        }

        // Si no hay un ID de empresa específico en los parámetros, usar el ID de empresa del usuario actual
        if (!params.companyId) {
            const currentCompanyId = authManager.companyId;
            if (currentCompanyId) {
                params.companyId = currentCompanyId;
                console.log(`Hash: Asignando empresa del usuario actual (ID: ${currentCompanyId})`);
            }
        }

        // Navegar a la pantalla
        navigateTo(screen, params);
    } else {
        console.warn(`Hash no reconocido: ${baseHash}, redirigiendo al menú principal`);
        // Si el hash no se reconoce, ir al menú principal
        navigateTo('main-menu');
    }
}

/**
 * Maneja el evento popstate (botón atrás/adelante del navegador)
 * Esta función se encarga de toda la navegación basada en hash
 */
function handlePopState() {
    console.log(`handlePopState: hash=${window.location.hash}`);

    // Si no hay usuario autenticado, salir
    if (!authManager.isAuthenticated()) {
        console.log('handlePopState: Usuario no autenticado, saliendo');
        return;
    }

    // Obtener el hash actual completo
    const fullHash = window.location.hash.substring(1);
    const hashParts = fullHash.split('/');
    const baseHash = hashParts[0];

    // Verificar si el hash actual es válido
    const isValidHash = baseHash && Object.keys(hashToScreenMap).includes(baseHash);

    // Quitar la pantalla actual de la pila UI
    popFromUIStack(false).then(lastElement => {
        // Si la pantalla actual es login, no hacer nada
        if (lastElement && lastElement.element.id === 'login-container') {
            return;
        }

        // Si la pantalla actual es el menú principal y no hay hash, interpretar como cierre de sesión
        if (lastElement && lastElement.element.id === 'main-menu-container' && !fullHash) {
            dialogManager.confirm('¿Desea cerrar la sesión?').then(confirmed => {
                if (confirmed) {
                    authManager.logout();
                } else {
                    navigateTo('main-menu');
                }
            });
            return;
        }

        // Si el hash es válido, navegar a esa pantalla
        if (isValidHash) {
            navigateToHash(fullHash);
        } else if (fullHash) {
            // Hash no reconocido, ir al menú principal
            console.warn(`handlePopState: Hash no reconocido: ${baseHash}, navegando al menú principal`);
            navigateTo('main-menu');
        } else {
            // Sin hash, ir al menú principal
            navigateTo('main-menu');
        }
    });
}

/**
 * Actualiza el hash de la URL según la pantalla actual
 * @param {string} screen - La pantalla actual
 * @param {Object} params - Parámetros adicionales
 */
function updateUrlHash(screen, params = {}) {
    let hash;
    let hashSuffix = '';

    // Manejar casos específicos primero
    if (screen === 'main-menu') {
        hash = 'menu';
    } else if (screen === 'entity-management') {
        // Para entity-management, verificar el tipo de entidad
        if (params.entityType === 'companies') {
            hash = 'empresas';
        } else if (params.entityType === 'usuarios') {
            hash = 'usuarios';

            // Si hay un ID de empresa específico, actualizar los parámetros para esta entidad
            if (params.companyId !== undefined) {
                // Asegurarse de que companyId sea un número
                const companyId = Number(params.companyId);

                // Guardar el ID de empresa en los parámetros para esta entidad
                screenParams[hash] = {
                    ...screenParams[hash],
                    companyId: companyId
                };

                // Si el ID de empresa es diferente al del usuario actual, añadirlo al hash
                const currentCompanyId = Number(authManager.companyId);
                if (companyId !== currentCompanyId) {
                    hashSuffix = `/${companyId}`;

                    // Si tenemos el nombre de la empresa, añadirlo también (opcional)
                    if (params.companyName) {
                        hashSuffix += `/${params.companyName}`;
                    }
                }
            } else {
                // Si no hay ID de empresa específico, usar el ID de empresa del usuario actual
                // Esto asegura que el hash 'usuarios' siempre cargue los usuarios de la empresa del usuario actual
                const currentCompanyId = authManager.companyId;
                if (currentCompanyId) {
                    screenParams[hash] = {
                        ...screenParams[hash],
                        companyId: currentCompanyId
                    };
                }
            }
        } else if (params.entityType === 'tableros') {
            hash = 'tableros';

            // Si hay un ID de empresa específico, actualizar los parámetros para esta entidad
            if (params.companyId !== undefined) {
                // Asegurarse de que companyId sea un número
                const companyId = Number(params.companyId);

                // Guardar el ID de empresa en los parámetros para esta entidad
                screenParams[hash] = {
                    ...screenParams[hash],
                    companyId: companyId
                };

                // Si el ID de empresa es diferente al del usuario actual, añadirlo al hash
                const currentCompanyId = Number(authManager.companyId);
                if (companyId !== currentCompanyId) {
                    hashSuffix = `/${companyId}`;

                    // Si tenemos el nombre de la empresa, añadirlo también (opcional)
                    if (params.companyName) {
                        hashSuffix += `/${params.companyName}`;
                    }
                }
            } else {
                // Si no hay ID de empresa específico, usar el ID de empresa del usuario actual
                // Esto asegura que el hash 'tableros' siempre cargue los tableros de la empresa del usuario actual
                const currentCompanyId = authManager.companyId;
                if (currentCompanyId) {
                    screenParams[hash] = {
                        ...screenParams[hash],
                        companyId: currentCompanyId
                    };
                }
            }
        } else {
            // Si no se especifica el tipo de entidad, usar un valor genérico
            hash = params.entityType || screen;
        }
    } else if (screen === 'dashboard') {
        hash = 'tablero';

        // Si hay un dashboardId en los parámetros, añadirlo al hash
        if (params.dashboardId) {
            hashSuffix = `/${params.dashboardId}`;
        }
    } else if (screen === 'login') {
        hash = 'login';
    } else {
        // Para otros casos, buscar en el mapa
        hash = Object.keys(hashToScreenMap).find(key => hashToScreenMap[key] === screen);

        // Si no se encuentra un hash específico, usar el nombre de la pantalla
        if (!hash) {
            hash = screen;
        }
    }

    // Combinar el hash base con el sufijo si existe
    const fullHash = hash + hashSuffix;

    // Actualizar el hash
    if (window.location.hash !== `#${fullHash}`) {
        history.pushState(null, '', `#${fullHash}`);
    }
}