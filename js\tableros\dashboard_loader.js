/**
 * Dashboard Loader
 *
 * Este archivo centraliza la carga de todos los scripts relacionados con los tableros (dashboards).
 * Proporciona una forma organizada de gestionar las dependencias entre archivos y facilita
 * la comprensión de la estructura del código para futuros desarrolladores.
 */

// Función para cargar scripts de forma secuencial
function loadScriptsSequentially(scripts, callback) {
    if (scripts.length === 0) {
        if (typeof callback === 'function') callback();
        return;
    }

    const script = document.createElement('script');
    script.src = scripts[0];
    script.onload = function () {
        console.log(`Script cargado: ${scripts[0]}`);
        loadScriptsSequentially(scripts.slice(1), callback);
    };
    script.onerror = function () {
        console.error(`Error al cargar script: ${scripts[0]}`);
        loadScriptsSequentially(scripts.slice(1), callback);
    };
    document.head.appendChild(script);
}

// Definir los scripts a cargar en el orden correcto
const dashboardScripts = [
    // Utilidades y extensiones HTML
    'js/utiles/html.js', // Funciones base para crear elementos HTML

    // Servicios y gestores de datos
    // 'js/data.js', // Servicio de datos para widgets
    'js/database_innerdb.js', // Gestor de base de datos

    // Componentes de la interfaz de usuario
    'js/tableros/form_fields_accordion.js', // Implementación de acordeón para campos de formulario
    'js/tableros/color-picker-handlers.js', //color pickers para el dashboard y los widgets

    // Gestores principales
    'js/tableros/widget_fields.js', // Gestor de campos de widgets
    'js/tableros/widgets.js', // Gestor de widgets
    'js/tableros/dashboard.js', // Gestor principal de tableros
    'js/tableros/dashboard_selection_manager.js', // Gestor de portapalees del dashboard

    //Mostrar formulario config widget como acordeón
    'js/tableros/widget_fields_accordion.js'
];

// Objeto para registrar los componentes cargados
window.dashboardComponents = {
    loaded: {},
    isReady: false,

    // Método para verificar si todos los componentes están cargados
    allComponentsLoaded: function () {
        return Object.keys(this.loaded).length === dashboardScripts.length;
    },

    // Método para marcar un componente como cargado
    markAsLoaded: function (scriptPath) {
        const scriptName = scriptPath.split('/').pop();
        this.loaded[scriptName] = true;

        if (this.allComponentsLoaded()) {
            this.isReady = true;
            console.log('Todos los componentes del dashboard han sido cargados');

            // Disparar evento personalizado
            document.dispatchEvent(new CustomEvent('dashboardReady'));
        }
    }
};

/**
 * Inicia la carga de todos los scripts relacionados con los tableros
 * @param {Function} callback - Función a ejecutar cuando todos los scripts estén cargados
 */
function initDashboardLoader(callback) {
    console.log('Iniciando carga de scripts de tableros...');

    loadScriptsSequentially(dashboardScripts, function () {
        console.log('Todos los scripts de tableros han sido cargados');

        if (typeof callback === 'function') {
            callback();
        }

        // Disparar evento de que el dashboard está listo
        document.dispatchEvent(new CustomEvent('dashboardReady'));
    });
}

// Documentación de los archivos incluidos
/**
 * Descripción de los archivos:
 *
 * js/utiles/html.js:
 *   Proporciona funciones utilitarias para crear elementos HTML de manera consistente.
 *   Incluye funciones como crearElemento(), crearBoton(), crearIcono(), etc.
 *
 * js/utiles/html_inputs.js:
 *   Extiende las utilidades HTML con funciones especializadas para crear inputs.
 *   Incluye funciones como crearInputText(), crearInputNumber(), crearCheckbox(), crearSelect(), etc.
 *
 * js/data.js:
 *   Servicio de datos que proporciona información para los widgets.
 *   Incluye métodos como getValueTypes(), getGaugeTypes(), getPercentageTypes(), etc.
 *
 * js/database.js:
 *   Gestor de base de datos para almacenar y recuperar configuraciones de tableros y widgets.
 *
 * js/tableros/form_fields_accordion.js:
 *   Implementa un componente de acordeón para mostrar campos de formulario agrupados por zonas.
 *   Proporciona una interfaz más organizada y amigable para configurar widgets.
 *
 * js/tableros/widget_fields.js:
 *   Define y gestiona los campos de configuración para cada tipo de widget.
 *   Incluye métodos para obtener campos, llenar formularios y recopilar parámetros.
 *
 * js/tableros/widget.js:
 *   Gestor principal de widgets. Maneja la creación, actualización, eliminación y renderizado de widgets.
 *
 * js/tableros/dashboard.js:
 *   Gestor principal de tableros. Maneja la creación, configuración y gestión de tableros.
 *   Incluye métodos para cambiar entre tableros, aplicar temas, etc.
 *
 * js/tableros/widget_fields_accordion.js:
 *   Extiende el gestor de campos de widgets para utilizar el componente de acordeón.
 *   Modifica los métodos fillWidgetFields() y getWidgetParams() para trabajar con el acordeón.
 */

// Alternativa: Módulos ES6
// Si el proyecto eventualmente migra a módulos ES6, se podría usar este enfoque:
/*
// Exportar función de inicialización
export function initDashboard() {
    // Código de inicialización
}

// En el archivo principal:
import { initDashboard } from './js/tableros/dashboard_loader.js';
initDashboard();
*/

// Exponer la función de inicialización globalmente
window.initDashboardLoader = initDashboardLoader;