/**
 * Módulo para la gestión de widgets
 */
class WidgetManager {
    constructor() {
        this.widgets = [];
        this.nextWidgetId = 1;

        // Registrar instancia en app
        app["widgetManager"] = this;

        // Registrar evento para actualizar gráficas cuando hay nuevos datos
        document.addEventListener('dataUpdated', () => {
            //  console.log('Evento dataUpdated recibido en WidgetManager');

            // Obtener todos los widgets de tipo gráfica
            const chartWidgets = document.querySelectorAll('.chart-widget');
            //console.log(`Widgets de gráfica encontrados: ${chartWidgets.length}`);

            // Si hay widgets de tipo gráfica, actualizar sus gráficas
            if (chartWidgets.length > 0) {
                chartWidgets.forEach(widgetElement => {
                    const widgetId = parseInt(widgetElement.dataset.widgetId);
                    const widget = this.getWidget(widgetId);

                    if (widget && widget.type === 'chart') {
                        //  console.log(`Actualizando widget de gráfica ID: ${widgetId}`);
                        const contentElement = widgetElement.querySelector('.widget-content');
                        if (contentElement) {
                            this.renderChartWidget(contentElement, widget);
                        } else {
                            console.warn(`No se encontró el contenedor para el widget ID: ${widgetId}`);
                        }
                    }
                });
            }

            // Obtener todos los widgets de tipo gráfica por períodos
            const periodChartWidgets = document.querySelectorAll('.period-chart-widget');

            // Si hay widgets de tipo gráfica por períodos, actualizarlos
            if (periodChartWidgets.length > 0) {
                periodChartWidgets.forEach(widgetElement => {
                    const widgetId = parseInt(widgetElement.dataset.widgetId);
                    const widget = this.getWidget(widgetId);

                    if (widget && widget.type === 'period-chart') {
                        const contentElement = widgetElement.querySelector('.widget-content');
                        if (contentElement) {
                            this.renderPeriodChartWidget(contentElement, widget);
                        } else {
                            console.warn(`No se encontró el contenedor para el widget ID: ${widgetId}`);
                        }
                    }
                });
            }

            // Obtener todos los widgets de tipo últimos valores
            const latestWidgets = document.querySelectorAll('.latest-widget');
            // console.log(`Widgets de últimos valores encontrados: ${latestWidgets.length}`);

            // Si hay widgets de tipo últimos valores, actualizarlos
            if (latestWidgets.length > 0) {
                latestWidgets.forEach(widgetElement => {
                    const widgetId = parseInt(widgetElement.dataset.widgetId);
                    const widget = this.getWidget(widgetId);

                    if (widget && widget.type === 'latest') {
                        //  console.log(`Actualizando widget de últimos valores ID: ${widgetId}`);
                        const contentElement = widgetElement.querySelector('.widget-content');
                        if (contentElement) {
                            this.renderLatestWidget(contentElement, widget);
                        } else {
                            console.warn(`No se encontró el contenedor para el widget ID: ${widgetId}`);
                        }
                    }
                });
            }

            // Obtener todos los widgets de tipo últimos por períodos
            const latestByPeriodsWidgets = document.querySelectorAll('.latest-by-periods-widget');

            // Si hay widgets de tipo últimos por períodos, actualizarlos
            if (latestByPeriodsWidgets.length > 0) {
                latestByPeriodsWidgets.forEach(widgetElement => {
                    const widgetId = parseInt(widgetElement.dataset.widgetId);
                    const widget = this.getWidget(widgetId);

                    if (widget && widget.type === 'latest-by-periods') {
                        const contentElement = widgetElement.querySelector('.widget-content');
                        if (contentElement) {
                            this.renderLatestByPeriodsWidget(contentElement, widget);
                        } else {
                            console.warn(`No se encontró el contenedor para el widget ID: ${widgetId}`);
                        }
                    }
                });
            }

            // También actualizar widgets de valores y medidores
            this.updateValueWidgets();
        });
    }

    /**
     * Obtiene los widgets del tablero actual
     * @returns {Array} - Lista de widgets
     */
    getWidgets() {
        return dashboardManager.dashboard.widgets || [];
    }

    /**
     * Guarda los widgets en el tablero actual
     * @param {Array} widgets - Lista de widgets a guardar
     */
    saveWidgets(widgets) {
        // Limpiar objetos no serializables antes de guardar
        const cleanWidgets = widgets.map(widget => {
            // Crear una copia del widget sin el objeto chart
            const {
                chart,
                ...cleanWidget
            } = widget;

            // Si hay series, asegurarse de que también se limpien
            if (cleanWidget.params && cleanWidget.params.series) {
                cleanWidget.params.series = cleanWidget.params.series.map(serie => {
                    // Eliminar cualquier propiedad no serializable de las series
                    const {
                        chart,
                        ...cleanSerie
                    } = serie;
                    return cleanSerie;
                });
            }

            return cleanWidget;
        });

        // Actualizar widgets en el dashboard directamente
        dashboardManager.dashboard.widgets = cleanWidgets;

        // Guardar el tablero completo para persistir los cambios
        dashboardManager.saveDashboard()
            .then(() => {
                console.log('Tablero guardado con widgets actualizados');
            })
            .catch(error => {
                console.error('Error al guardar el tablero con widgets actualizados:', error);
            });
    }

    /**
     * Elimina los widgets del tablero actual
     */
    clearWidgets() {
        dashboardManager.dashboard.widgets = [];
        dashboardManager.saveDashboard();
    }

    /**
     * Añade un widget al tablero actual
     * @param {Object} widget - Widget a añadir
     */
    addWidget(widget) {
        const widgets = this.getWidgets();
        widgets.push(widget);
        this.saveWidgets(widgets);
    }

    /**
     * Elimina un widget del tablero actual
     * @param {number} widgetId - ID del widget a eliminar
     */
    deleteWidget(widgetId) {
        const widgets = this.getWidgets();
        const index = widgets.findIndex(w => w.id === widgetId);
        if (index !== -1) {
            widgets.splice(index, 1);
            this.saveWidgets(widgets);
        }
    }

    /**
     * Actualiza un widget en el tablero actual
     * @param {number} widgetId - ID del widget a actualizar
     * @param {Object} updates - Actualizaciones a aplicar
     */
    updateWidget(widgetId, updates) {
        const widgets = this.getWidgets();
        const index = widgets.findIndex(w => w.id === widgetId);
        if (index !== -1) {
            // Preservar el array de series si existe y no se proporciona uno nuevo
            if (widgets[index].type === 'chart' && widgets[index].params && widgets[index].params.series) {
                if (!updates.params) {
                    updates.params = {};
                }
                if (!updates.params.series) {
                    updates.params.series = [...widgets[index].params.series];
                    console.log('Series preservadas en updateWidget:', updates.params.series);
                }
            }

            // Manejar específicamente la actualización de estilos para evitar problemas con objetos anidados
            if (updates.style) {
                // Crear una copia profunda del estilo actual
                const currentStyle = widgets[index].style || {};

                // Crear un nuevo objeto de estilo combinando el actual con las actualizaciones
                const newStyle = {
                    backgroundColor: updates.style.backgroundColor !== undefined ? updates.style.backgroundColor : currentStyle.backgroundColor,
                    textColor: updates.style.textColor !== undefined ? updates.style.textColor : currentStyle.textColor,
                    borderColor: updates.style.borderColor !== undefined ? updates.style.borderColor : currentStyle.borderColor
                };

                // Asignar el nuevo estilo al widget
                widgets[index].style = newStyle;

                console.log('Estilo actualizado manualmente:', newStyle);

                // Eliminar style de updates para evitar sobrescribirlo con el spread general
                const {
                    style,
                    ...otherUpdates
                } = updates;

                // Actualizar el resto de propiedades
                widgets[index] = {
                    ...widgets[index],
                    ...otherUpdates
                };

                if (widgets[index].params.x != undefined) widgets[index].x = widgets[index].params.x;
                if (widgets[index].params.y != undefined) widgets[index].y = widgets[index].params.x;
                if (widgets[index].params.width != undefined) widgets[index].width = widgets[index].params.width;
                if (widgets[index].params.height != undefined) widgets[index].height = widgets[index].params.height;

                // Asegurarse de que el estilo no se haya perdido en el spread
                widgets[index].style = newStyle;
            } else {
                // Si no hay actualizaciones de estilo, usar el spread normal
                widgets[index] = {
                    ...widgets[index],
                    ...updates
                };
            }
            this.saveWidgets(widgets);
        }
    }

    /**
     * Obtiene un widget por su ID
     * @param {number} widgetId - ID del widget
     * @returns {Object|null} - Widget encontrado o null si no existe
     */
    getWidget(widgetId) {
        const widgets = this.getWidgets();
        return widgets.find(w => w.id === widgetId) || null;
    }

    /**
     * Actualiza el nextWidgetId basado en los widgets existentes
     * para evitar IDs duplicados
     */
    updateNextWidgetId() {
        const widgets = this.getWidgets();
        if (widgets.length === 0) {
            // Si no hay widgets, reiniciar a 1
            this.nextWidgetId = 1;
            return;
        }

        // Encontrar el máximo ID entre los widgets existentes
        let maxId = 0;
        widgets.forEach(widget => {
            // Convertir a número si es una cadena
            const widgetId = typeof widget.id === 'string' ? parseInt(widget.id) : widget.id;
            if (!isNaN(widgetId) && widgetId > maxId) {
                maxId = widgetId;
            }
        });

        // Establecer nextWidgetId como el máximo + 1
        this.nextWidgetId = maxId + 1;
        // console.log(`nextWidgetId actualizado a ${this.nextWidgetId} basado en widgets existentes`);
    }

    /**
     * Renderiza todos los widgets en el dashboard
     * @param {HTMLElement} dashboardElement - Elemento del dashboard
     */
    renderWidgets(dashboardElement) {
        // Actualizar nextWidgetId basado en los widgets existentes
        this.updateNextWidgetId();

        // Limpiar dashboard
        dashboardElement.innerHTML = '';

        // Renderizar cada widget
        const widgets = this.getWidgets();

        widgets.forEach(widget => {         
            const widgetElement = this.createWidgetElement(widget);
            dashboardElement.appendChild(widgetElement);
        });
    }

    /**
     * Genera un nombre automático para el widget
     * @param {string} type - Tipo de widget
     * @param {Object} params - Parámetros del widget
     * @returns {Promise<string>} - Promesa que se resuelve con el nombre generado
     */
    generateWidgetName(type, params) {
        return new Promise((resolve) => {
            let prefix = '';
            let valueName = '';

            // Determinar el prefijo según el tipo
            switch (type) {
                case 'text':
                    prefix = 'T';
                    valueName = 'Texto';
                    break;
                case 'value':
                    prefix = 'V';
                    const valueType = params.valueType;
                    const valueInfo = dataService.getValueTypes().find(t => t.id === valueType);
                    valueName = valueInfo ? valueInfo.name : valueType;
                    break;
                case 'gauge':
                    prefix = 'G';
                    const gaugeType = params.gaugeType;
                    const gaugeInfo = dataService.getGaugeTypes().find(t => t.id === gaugeType);
                    valueName = gaugeInfo ? gaugeInfo.name : gaugeType;
                    break;
                case 'percentage-gauge':
                    prefix = 'GP';
                    const percentageType = params.percentageType;
                    const percentageInfo = dataService.getPercentageTypes().find(t => t.id === percentageType);
                    valueName = percentageInfo ? percentageInfo.name : percentageType;
                    break;
                case 'chart':
                    prefix = 'C';
                    const chartType = params.chartType;
                    const chartInfo = dataService.getValueTypes().find(t => t.id === chartType);
                    valueName = chartInfo ? chartInfo.name : chartType;
                    break;
                case 'latest':
                    prefix = 'L';
                    const latestType = params.latestType;
                    const latestInfo = dataService.getValueTypes().find(t => t.id === latestType);
                    valueName = latestInfo ? latestInfo.name : latestType;
                    break;
                case 'period-chart':
                    prefix = 'PC';
                    const periodChartType = params.periodChartType;
                    const periodChartInfo = dataService.getValueTypes().find(t => t.id === periodChartType);
                    valueName = periodChartInfo ? periodChartInfo.name : periodChartType;
                    break;
                case 'latest-by-periods':
                    prefix = 'LP';
                    const latestByPeriodsType = params.latestByPeriodsType;
                    const latestByPeriodsInfo = dataService.getValueTypes().find(t => t.id === latestByPeriodsType);
                    valueName = latestByPeriodsInfo ? latestByPeriodsInfo.name : latestByPeriodsType;
                    break;
            }

            // Obtener el número secuencial
            dashboardManager.incrementWidgetCount()
                .then(count => {
                    resolve(`${prefix}_${valueName}_${count}`);
                })
                .catch(error => {
                    console.error('Error al incrementar el contador de widgets:', error);
                    // En caso de error, usar un valor por defecto
                    resolve(`${prefix}_${valueName}_${Date.now()}`);
                });
        });
    }

    /**
     * Establece las dimensiones predeterminadas de un widget según su tipo
     * @param {string} widgetType - Tipo de widget
     * @param {Object} widthInput - Objeto con propiedad value para el ancho
     * @param {Object} heightInput - Objeto con propiedad value para la altura
     */
    setDefaultWidgetDimensions(widgetType, widthInput, heightInput) {
        switch (widgetType) {
            case 'text':
                if (widthInput) widthInput.value = 300;
                if (heightInput) heightInput.value = 64;
                break;
            case 'value':
                if (widthInput) widthInput.value = 200;
                if (heightInput) heightInput.value = 120;
                break;
            case 'gauge':
                if (widthInput) widthInput.value = 200;
                if (heightInput) heightInput.value = 200;
                break;
            case 'percentage-gauge':
                if (widthInput) widthInput.value = 350;
                if (heightInput) heightInput.value = 120;
                break;
            case 'chart':
                if (widthInput) widthInput.value = 400;
                if (heightInput) heightInput.value = 300;
                break;
            case 'latest':
                if (widthInput) widthInput.value = 350;
                if (heightInput) heightInput.value = 200;
                break;
            case 'period-chart':
                if (widthInput) widthInput.value = 400;
                if (heightInput) heightInput.value = 300;
                break;
            case 'latest-by-periods':
                if (widthInput) widthInput.value = 350;
                if (heightInput) heightInput.value = 200;
                break;
        }
    }

    /**
     * Crea un nuevo widget
     * @param {Object} widgetConfig - Configuración del widget
     * @returns {Promise<Object>} - Promesa que se resuelve con el widget creado
     */
    createWidget(widgetConfig) {
        // Asegurarse de que params y style tengan valores por defecto si son nulos o indefinidos
        const params = widgetConfig.params || {};
        const style = widgetConfig.style || {
            backgroundColor: "defecto",
            textColor: "defecto",
            borderColor: "defecto"
        };

        // Inicializar el array de series para widgets de tipo chart
        if (widgetConfig.type === 'chart' && !params.series) {
            params.series = [];
        }

        // Si style existe pero no tiene todas las propiedades, asegurarse de que se inicialicen
        if (style) {
            style.backgroundColor = style.backgroundColor !== undefined ? style.backgroundColor : "defecto";
            style.textColor = style.textColor !== undefined ? style.textColor : "defecto";
            style.borderColor = style.borderColor !== undefined ? style.borderColor : "defecto";
        }

        // Determinar la altura predeterminada según el tipo si no se especifica
        let height = widgetConfig.height;
        if (height === null || height === undefined) {
            switch (widgetConfig.type) {
                case 'text':
                    height = 64;
                    break;
                default:
                    height = 150;
                    break;
            }
        }

        // Generar nombre automático y crear el widget
        return this.generateWidgetName(widgetConfig.type, widgetConfig.params)
            .then(autoName => {
                const widget = {
                    id: this.nextWidgetId++,
                    name: autoName, // Usar nombre generado automáticamente
                    type: widgetConfig.type,
                    width: widgetConfig.width,
                    height: height,
                    x: widgetConfig.x || 0,
                    y: widgetConfig.y || 0,
                    params: params,
                    style: style
                };

                this.addWidget(widget);
                return widget;
            })
            .catch(error => {
                console.error('Error al crear widget:', error);
                // En caso de error, crear un widget con un nombre por defecto
                const widget = {
                    id: this.nextWidgetId++,
                    name: `Widget_${Date.now()}`, // Nombre por defecto
                    type: widgetConfig.type,
                    width: widgetConfig.width,
                    height: height,
                    x: widgetConfig.x || 0,
                    y: widgetConfig.y || 0,
                    params: params,
                    style: style
                };

                this.addWidget(widget);
                return widget;
            });
    }

    /**
     * Actualiza la posición de un widget
     * @param {number} widgetId - ID del widget
     * @param {number} x - Posición X
     * @param {number} y - Posición Y
     */
    updateWidgetPosition(widgetId, x, y) {
        const widgets = this.getWidgets();
        const widget = widgets.find(w => w.id === widgetId);
        if (widget) {
            widget.x = x;
            widget.y = y;
            this.saveWidgets(widgets);
        }
    }

    /**
     * Duplica un widget existente
     * @param {number} widgetId - ID del widget a duplicar
     * @returns {Promise<Object|null>} - Promesa que se resuelve con el widget duplicado o null si no se pudo duplicar
     */
    duplicateWidget(widgetId) {
        return new Promise((resolve, reject) => {
            const originalWidget = this.getWidget(widgetId);
            if (!originalWidget) {
                resolve(null);
                return;
            }

            // Obtener dimensiones del dashboard
            const dashboard = app.dashboardManager.dashboardElement;
            const dashboardRect = dashboard.getBoundingClientRect();
            const dashboardWidth = dashboardRect.width;
            const dashboardHeight = dashboardRect.height;
            // Calcular nueva posición para el widget duplicado (10px a la derecha y abajo)
            let x = originalWidget.x + 10;
            let y = originalWidget.y + 10;

            console.log("Initial new position:", x, y);

            // Verificar si el widget cabe en la nueva posición
            if (x + originalWidget.width > dashboardWidth || y + originalWidget.height > dashboardHeight) {
                // Si no cabe, colocarlo 10px a la izquierda y arriba
                x = Math.max(0, originalWidget.x - 10);
                y = Math.max(0, originalWidget.y - 10);
                console.log("Adjusted position (not fitting):", x, y);
            }

            // Asegurarse de que el widget esté dentro de los límites del dashboard
            x = Math.min(x, dashboardWidth - originalWidget.width);
            y = Math.min(y, dashboardHeight - originalWidget.height);

            console.log("Final position:", x, y);

            // Asegurarse de que las coordenadas sean números válidos
            if (isNaN(x)) x = 0;
            if (isNaN(y)) y = 0;

            console.log("Final position for duplicate widget:", x, y);

            // Crear una copia profunda del widget original
            const widgetConfig = {
                type: originalWidget.type,
                width: originalWidget.width,
                height: originalWidget.height,
                x: x,
                y: y,
                params: originalWidget.params ? JSON.parse(app.stringify(originalWidget.params)) : {},
                style: originalWidget.style ? JSON.parse(app.stringify(originalWidget.style)) : {
                    backgroundColor: "defecto",
                    textColor: "defecto",
                    borderColor: "defecto"
                }
            };

            // Crear el nuevo widget
            this.createWidget(widgetConfig)
                .then(newWidget => {
                    // Renderizar los widgets
                    const dashboardElement = app.dashboardManager.dashboardElement;
                    if (dashboardElement) {
                        this.renderWidgets(dashboardElement);
                    }
                    resolve(newWidget);
                })
                .catch(error => {
                    console.error('Error al duplicar widget:', error);
                    reject(error);
                });
        });
    }

    /**
     * Crea el elemento HTML para un widget
     * @param {Object} widget - Configuración del widget
     * @returns {HTMLElement} - Elemento del widget
     */
    createWidgetElement(widget) {
        const widgetElement = document.createElement('div');
        widgetElement.className = `widget ${widget.type}-widget`;
        widgetElement.id = `widget-${widget.id}`;
        widgetElement.dataset.widgetId = widget.id;

        // Establecer dimensiones
        widgetElement.style.width = `${widget.width}px`;
        widgetElement.style.height = `${widget.height}px`;

        // Obtener dimensiones del dashboard
        const dashboard = app.dashboardManager.dashboardElement;
        const dashboardRect = dashboard.getBoundingClientRect();
        const dashboardWidth = dashboardRect.width;
        const dashboardHeight = dashboardRect.height;

        console.log("Dashboard dimensions in createWidgetElement:", dashboardWidth, dashboardHeight);
        console.log("Widget position before adjustment:", widget.x, widget.y);

        // Usar las posiciones exactas guardadas en el widget sin modificaciones
        let x = typeof widget.x === 'number' ? widget.x : 0;
        let y = typeof widget.y === 'number' ? widget.y : 0;

        // Si son NaN, establecerlos a 0
        if (isNaN(x)) x = 0;
        if (isNaN(y)) y = 0;

        console.log("Widget position (using saved values):", x, y);

        // Establecer posición exacta sin ajustes
        widgetElement.style.left = `${x}px`;
        widgetElement.style.top = `${y}px`;

        console.log("Widget posicionado en:", x, y, "Widget ID:", widget.id);

        // Verificar si hay reglas definidas y aplicarlas
        let applyRules = false;
        let ruleColors = null;

        if (widget.params) {
            // Obtener el valor para evaluar las reglas
            let valorParaReglas = null;

            // No aplicar reglas a widgets de texto
            if (widget.type === 'text') {
                valorParaReglas = null;
            } else {
                // Obtener el valor del widget según su tipo
                switch (widget.type) {
                    case 'value':
                        if (widget.params.valueType) {
                            const valueData = dataService.getValue(widget.params.valueType);
                            if (valueData) valorParaReglas = valueData.value;
                        }
                        break;
                    case 'gauge':
                        if (widget.params.gaugeType) {
                            const gaugeData = dataService.getValue(widget.params.gaugeType);
                            if (gaugeData) valorParaReglas = gaugeData.value;
                        }
                        break;
                    case 'percentage-gauge':
                        if (widget.params.percentageType) {
                            const percentageData = dataService.getValue(widget.params.percentageType);
                            if (percentageData) valorParaReglas = percentageData.value;
                        }
                        break;
                        // Otros tipos de widgets podrían añadirse aquí
                }
            }

            // Si tenemos un valor para evaluar y hay reglas definidas
            if (valorParaReglas !== null) {
                console.log(`Evaluando reglas para widget ${widget.id} con valor: ${valorParaReglas}`);

                // Verificar regla 1
                if (widget.params.regla1Valor !== undefined && widget.params.regla1Valor !== null && widget.params.regla1Valor !== '') {
                    const regla1Valor = parseFloat(widget.params.regla1Valor);

                    // Verificar regla 2
                    if (widget.params.regla2Valor !== undefined && widget.params.regla2Valor !== null && widget.params.regla2Valor !== '') {
                        const regla2Valor = parseFloat(widget.params.regla2Valor);

                        // Si el valor es menor o igual que regla1Valor, aplicar colores de regla 1
                        if (valorParaReglas <= regla1Valor) {
                            applyRules = true;
                            ruleColors = {
                                backgroundColor: widget.params.regla1BgColor,
                                textColor: widget.params.regla1TextColor,
                                borderColor: widget.params.regla1BorderColor
                            };
                            console.log(`Aplicando regla 1 (valor <= ${regla1Valor})`);
                        }
                        // Si el valor es menor o igual que regla2Valor pero mayor que regla1Valor, aplicar colores de regla 2
                        else if (valorParaReglas <= regla2Valor) {
                            applyRules = true;
                            ruleColors = {
                                backgroundColor: widget.params.regla2BgColor,
                                textColor: widget.params.regla2TextColor,
                                borderColor: widget.params.regla2BorderColor
                            };
                            console.log(`Aplicando regla 2 (${regla1Valor} < valor <= ${regla2Valor})`);
                        }
                        // Si el valor es mayor que regla2Valor, usar colores normales
                    }
                    // Si solo hay regla 1 definida
                    else if (valorParaReglas <= regla1Valor) {
                        applyRules = true;
                        ruleColors = {
                            backgroundColor: widget.params.regla1BgColor,
                            textColor: widget.params.regla1TextColor,
                            borderColor: widget.params.regla1BorderColor
                        };
                        console.log(`Aplicando regla 1 (valor <= ${regla1Valor})`);
                    }
                }
                // Si solo hay regla 2 definida
                else if (widget.params.regla2Valor !== undefined && widget.params.regla2Valor !== null && widget.params.regla2Valor !== '') {
                    const regla2Valor = parseFloat(widget.params.regla2Valor);

                    if (valorParaReglas <= regla2Valor) {
                        applyRules = true;
                        ruleColors = {
                            backgroundColor: widget.params.regla2BgColor,
                            textColor: widget.params.regla2TextColor,
                            borderColor: widget.params.regla2BorderColor
                        };
                        console.log(`Aplicando regla 2 (valor <= ${regla2Valor})`);
                    }
                }
            }
        }

        // Aplicar estilos personalizados si existen
        if (widget.style) {
            // Si hay reglas que aplicar, usar los colores de las reglas
            if (applyRules && ruleColors) {
                // Aplicar color de fondo de la regla
                if (ruleColors.backgroundColor && ruleColors.backgroundColor !== "defecto") {
                    widgetElement.style.backgroundColor = ruleColors.backgroundColor;
                    widgetElement.style.opacity = '1';
                }

                // Aplicar color de borde de la regla
                if (ruleColors.borderColor && ruleColors.borderColor !== "defecto") {
                    widgetElement.style.border = `1px solid ${ruleColors.borderColor}`;
                    widgetElement.classList.add('force-border');
                }

                // Aplicar color de texto de la regla
                if (ruleColors.textColor && ruleColors.textColor !== "defecto") {
                    widgetElement.style.color = ruleColors.textColor;
                }
            }
            // Si no hay reglas o no se cumplen, aplicar los estilos normales
            else {
                // Aplicar color de fondo personalizado
                if (widget.style.backgroundColor && widget.style.backgroundColor !== "defecto") {
                    // Forzar el color de fondo independientemente de la configuración del tablero
                    widgetElement.style.backgroundColor = widget.style.backgroundColor;
                    // Asegurar que el widget no sea transparente
                    widgetElement.style.opacity = '1';
                } else if (widget.style.backgroundColor === "defecto") {
                    // Si es "defecto", verificar si el tablero tiene activada la opción de widgets transparentes
                    if (dashboardManager.dashboard.transparentWidgets) {
                        // Si el tablero tiene activada la opción de widgets transparentes, quitar el estilo para usar el del tema
                        widgetElement.style.removeProperty('background-color');
                    } else if (dashboardManager.dashboard.widgetBgColor) {
                        // Si el tablero no tiene activada la opción de widgets transparentes y hay un color de fondo definido, usarlo
                        widgetElement.style.backgroundColor = dashboardManager.dashboard.widgetBgColor;
                        // Asegurar que el widget no sea transparente
                        widgetElement.style.opacity = '1';
                    } else {
                        // Si no hay color de fondo definido en el tablero, quitar el estilo para usar el del tema
                        widgetElement.style.removeProperty('background-color');
                    }
                } else {
                    // Si no hay color de fondo personalizado, quitar el estilo para usar el del tema
                    widgetElement.style.removeProperty('background-color');
                }

                // Aplicar color de borde personalizado
                if (widget.style.borderColor && widget.style.borderColor !== "defecto") {
                    // Forzar el color de borde independientemente de la configuración del tablero
                    widgetElement.style.border = `1px solid ${widget.style.borderColor}`;
                    // Asegurar que el borde sea visible
                    widgetElement.classList.add('force-border');
                } else {
                    // Si es "defecto" o no hay color de borde personalizado, quitar el estilo para usar el del tema
                    widgetElement.style.removeProperty('border');
                    widgetElement.classList.remove('force-border');
                }

                // Aplicar color de texto al widget principal
                // Esto asegura que el color se aplique a todo el contenido del widget
                console.log(`Aplicando color de texto al widget ${widget.id}. Valor actual:`, widget.style.textColor);
                console.log(`Widget completo:`, widget);
                console.log(`Estilo completo:`, widget.style);
                console.log(`Color de texto del tablero:`, dashboardManager.dashboard.widgetTextColor);

                if (widget.style.textColor && widget.style.textColor !== "defecto") {
                    console.log(`Aplicando color de texto personalizado ${widget.style.textColor} al widget ${widget.id}`);
                    widgetElement.style.color = widget.style.textColor;
                    console.log(`Color aplicado:`, widgetElement.style.color);
                } else if (widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
                    // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
                    console.log(`Aplicando color de texto del tablero ${dashboardManager.dashboard.widgetTextColor} al widget ${widget.id}`);
                    widgetElement.style.color = dashboardManager.dashboard.widgetTextColor;
                    console.log(`Color aplicado:`, widgetElement.style.color);
                } else {
                    console.log(`No hay color de texto definido para el widget ${widget.id}, usando el del tema`);
                    widgetElement.style.removeProperty('color');
                    console.log(`Color después de removeProperty:`, widgetElement.style.color);
                }
            }

            // El color de texto también se aplicará a los elementos internos en cada renderizador específico
        } else {
            // Si no hay estilos, inicializar la propiedad style
            widget.style = {
                backgroundColor: "defecto",
                textColor: "defecto",
                borderColor: "defecto"
            };
        }

        // Crear cabecera del widget si tiene título
        if (widget.params && widget.params.title) {
            const widgetHeader = document.createElement('div');
            widgetHeader.className = 'widget-header';

            const widgetTitle = document.createElement('div');
            widgetTitle.className = 'widget-title';
            widgetTitle.textContent = widget.params.title;

            // Aplicar color de texto basado en reglas o personalizado al título
            if (applyRules && ruleColors && ruleColors.textColor && ruleColors.textColor !== "defecto") {
                // Si hay reglas activas, aplicar el color de texto de la regla
                widgetTitle.style.color = ruleColors.textColor;
            } else if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                // Si no hay reglas activas pero hay color personalizado, aplicarlo
                widgetTitle.style.color = widget.style.textColor;
            } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
                // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
                widgetTitle.style.color = dashboardManager.dashboard.widgetTextColor;
            }

            widgetHeader.appendChild(widgetTitle);
            widgetElement.appendChild(widgetHeader);
        }

        // Crear contenido del widget
        const widgetContent = document.createElement('div');
        widgetContent.className = 'widget-content';

        // Renderizar contenido según el tipo de widget
        switch (widget.type) {
            case 'text':
                this.renderTextWidget(widgetContent, widget);
                break;
            case 'value':
                this.renderValueWidget(widgetContent, widget);
                break;
            case 'gauge':
                this.renderGaugeWidget(widgetContent, widget);
                break;
            case 'percentage-gauge':
                this.renderPercentageGaugeWidget(widgetContent, widget);
                break;
            case 'chart':
                this.renderChartWidget(widgetContent, widget);
                break;
            case 'latest':
                this.renderLatestWidget(widgetContent, widget);
                break;
            case 'period-chart':
                this.renderPeriodChartWidget(widgetContent, widget);
                break;
            case 'latest-by-periods':
                this.renderLatestByPeriodsWidget(widgetContent, widget);
                break;
        }

        //Poner un contenedor para usar en modo seleccioń
        const wdSeleccionContainer = document.createElement('div');
        wdSeleccionContainer.className = 'widget-checkbox-container';
        wdSeleccionContainer.innerHTML = '<input type="checkbox" class="widget-selector" aria-label="Seleccionar widget">';
        widgetElement.appendChild(wdSeleccionContainer);

        //Añadir contenido del widget
        widgetElement.appendChild(widgetContent);

        // Añadir manejador de redimensionamiento
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        widgetElement.appendChild(resizeHandle);

        // Hacer el widget arrastrable
        this.makeWidgetDraggable(widgetElement);

        // Hacer el widget redimensionable
        this.makeWidgetResizable(widgetElement, resizeHandle);

        // El modo edición ahora se activa automáticamente al comenzar a arrastrar
        // No necesitamos un evento de clic separado

        // Añadir evento de doble clic para editar el widget
        widgetElement.addEventListener('dblclick', app.dashboardManager.handleWidgetDoubleClick);

        return widgetElement;
    }

    /**
     * Renderiza un widget de texto
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderTextWidget(container, widget) {
        const textContent = widget.params.text || '';
        container.textContent = textContent;

        // Obtener el elemento widget padre
        const widgetElement = container.closest('.widget');

        // Verificar si el widget tiene un color de texto personalizado
        console.log(`Widget ${widget.id} - Estilo:`, widget.style);

        // Aplicar color de texto personalizado si existe
        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
            console.log(`Aplicando color de texto personalizado ${widget.style.textColor} al widget de texto ${widget.id}`);
            container.style.color = widget.style.textColor;

            // Asegurar que el color se aplique también al contenedor padre
            if (widgetElement) {
                widgetElement.style.color = widget.style.textColor;
                // Forzar el color con !important para evitar que sea sobrescrito
                widgetElement.setAttribute('style', widgetElement.getAttribute('style') + '; color: ' + widget.style.textColor + ' !important;');
            }
        } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
            // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
            console.log(`Aplicando color de texto del tablero ${dashboardManager.dashboard.widgetTextColor} al widget de texto ${widget.id}`);
            container.style.color = dashboardManager.dashboard.widgetTextColor;

            // Asegurar que el color se aplique también al contenedor padre
            if (widgetElement) {
                widgetElement.style.color = dashboardManager.dashboard.widgetTextColor;
            }
        } else if (!widget.style) {
            // Si no hay estilos, inicializar la propiedad style
            widget.style = {
                backgroundColor: "defecto",
                textColor: "defecto",
                borderColor: "defecto"
            };
            container.style.removeProperty('color');
        } else {
            // Si hay estilos pero el color de texto es "defecto" o no existe, usar el color por defecto del tema
            console.log(`No hay color de texto definido para el widget de texto ${widget.id}, usando el del tema`);
            container.style.removeProperty('color');
        }

        // Ajustar altura automáticamente para una línea de texto
        if (widget.height === 'auto') {
            // Crear un elemento temporal para medir la altura del texto
            const tempElement = document.createElement('div');
            tempElement.style.position = 'absolute';
            tempElement.style.visibility = 'hidden';
            tempElement.style.whiteSpace = 'nowrap';
            tempElement.style.fontSize = '16px'; // Mismo tamaño que en CSS
            tempElement.textContent = textContent;
            document.body.appendChild(tempElement);

            // Obtener altura y añadir padding
            const textHeight = tempElement.offsetHeight + 20; // 10px de padding arriba y abajo

            // Eliminar elemento temporal
            document.body.removeChild(tempElement);

            // Actualizar altura del widget
            const widgetElement = container.closest('.widget');
            if (widgetElement) {
                widgetElement.style.height = `${textHeight}px`;

                // Actualizar la configuración del widget
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidgets().find(w => w.id === widgetId);
                if (widget) {
                    widget.height = textHeight;
                    this.saveWidgets(this.getWidgets());
                }
            }
        }
    }

    /**
     * Renderiza un widget de valor
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderValueWidget(container, widget) {
        const valueType = widget.params.valueType;
        if (!valueType) return;

        const valueData = dataService.getValue(valueType);

        const valueElement = document.createElement('div');
        valueElement.className = 'value-display';
        valueElement.textContent = `${valueData.value} ${valueData.unit}`;

        // Obtener el elemento widget padre
        const widgetElement = container.closest('.widget');

        // Verificar si el widget tiene un color de texto personalizado
        console.log(`Widget ${widget.id} - Estilo:`, widget.style);

        // Aplicar color de texto personalizado si existe
        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
            console.log(`Aplicando color de texto personalizado ${widget.style.textColor} al widget de valor ${widget.id}`);
            valueElement.style.color = widget.style.textColor;

            // Asegurar que el color se aplique también al contenedor padre
            if (widgetElement) {
                widgetElement.style.color = widget.style.textColor;
                // Forzar el color con !important para evitar que sea sobrescrito
                widgetElement.setAttribute('style', widgetElement.getAttribute('style') + '; color: ' + widget.style.textColor + ' !important;');
            }
        } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
            // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
            console.log(`Aplicando color de texto del tablero ${dashboardManager.dashboard.widgetTextColor} al widget de valor ${widget.id}`);
            valueElement.style.color = dashboardManager.dashboard.widgetTextColor;

            // Asegurar que el color se aplique también al contenedor padre
            if (widgetElement) {
                widgetElement.style.color = dashboardManager.dashboard.widgetTextColor;
            }
        } else {
            // Si el color de texto es "defecto" o no existe, usar el color por defecto del tema
            console.log(`No hay color de texto definido para el widget de valor ${widget.id}, usando el del tema`);
            valueElement.style.removeProperty('color');
        }

        container.innerHTML = '';
        container.appendChild(valueElement);
    }

    /**
     * Renderiza un widget de gauge
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderGaugeWidget(container, widget) {
        const gaugeType = widget.params.gaugeType;
        if (!gaugeType) return;

        const gaugeData = dataService.getValue(gaugeType);
        const gaugeTypeInfo = dataService.getGaugeTypes().find(type => type.id === gaugeType);

        if (!gaugeTypeInfo) return;

        const {
            min,
            max
        } = gaugeTypeInfo;
        const value = gaugeData.value;

        // Crear contenedor del gauge
        const gaugeContainer = document.createElement('div');
        gaugeContainer.className = 'gauge-container';

        // Crear SVG para el gauge
        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.createElementNS(svgNS, "svg");
        svg.setAttribute("width", "100%");
        svg.setAttribute("height", "100%");
        svg.setAttribute("viewBox", "0 0 100 100");

        // Calcular el ángulo del gauge
        const startAngle = -90;
        const endAngle = 90;
        const angleRange = endAngle - startAngle;
        const valuePercentage = (value - min) / (max - min);
        const valueAngle = startAngle + (angleRange * valuePercentage);

        // Crear arco de fondo
        const backgroundArc = document.createElementNS(svgNS, "path");
        backgroundArc.setAttribute("class", "gauge-arc gauge-background");
        backgroundArc.setAttribute("d", this.describeArc(50, 50, 40, startAngle, endAngle));

        // Crear arco de valor
        const valueArc = document.createElementNS(svgNS, "path");
        valueArc.setAttribute("class", "gauge-arc gauge-foreground");
        valueArc.setAttribute("d", this.describeArc(50, 50, 40, startAngle, valueAngle));

        // Añadir arcos al SVG
        svg.appendChild(backgroundArc);
        svg.appendChild(valueArc);

        // Añadir valor numérico
        const valueText = document.createElement('div');
        valueText.className = 'gauge-value';
        valueText.textContent = `${value} ${gaugeData.unit}`;

        // Aplicar color de texto personalizado si existe
        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
            valueText.style.color = widget.style.textColor;
        } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
            // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
            valueText.style.color = dashboardManager.dashboard.widgetTextColor;
        } else {
            // Si el color de texto es "defecto" o no existe, usar el color por defecto del tema
            valueText.style.removeProperty('color');
        }

        // Añadir elementos al contenedor
        gaugeContainer.appendChild(svg);
        gaugeContainer.appendChild(valueText);

        container.innerHTML = '';
        container.appendChild(gaugeContainer);
    }

    /**
     * Renderiza un widget de gauge porcentual
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderPercentageGaugeWidget(container, widget) {
        const percentageType = widget.params.percentageType;
        if (!percentageType) return;

        const percentageData = dataService.getValue(percentageType);
        const value = percentageData.value;

        // Crear contenedor del gauge porcentual
        const gaugeContainer = document.createElement('div');
        gaugeContainer.className = 'percentage-gauge-container';

        // Crear título del valor
        const valueTitle = document.createElement('div');
        valueTitle.className = 'percentage-title';
        const typeInfo = dataService.getPercentageTypes().find(type => type.id === percentageType);
        valueTitle.textContent = typeInfo ? typeInfo.name : percentageType;

        // Aplicar color de texto personalizado al título si existe
        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
            valueTitle.style.color = widget.style.textColor;
        } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
            // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
            valueTitle.style.color = dashboardManager.dashboard.widgetTextColor;
        } else {
            // Si el color de texto es "defecto" o no existe, usar el color por defecto del tema
            valueTitle.style.removeProperty('color');
        }

        // Crear etiqueta de valor
        const valueLabel = document.createElement('div');
        valueLabel.className = 'percentage-value';
        valueLabel.textContent = `${value}%`;

        // Aplicar color de texto personalizado al valor si existe
        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
            valueLabel.style.color = widget.style.textColor;
        } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
            // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
            valueLabel.style.color = dashboardManager.dashboard.widgetTextColor;
        } else {
            // Si el color de texto es "defecto" o no existe, usar el color por defecto del tema
            valueLabel.style.removeProperty('color');
        }

        // Crear barra de progreso
        const progressBar = document.createElement('div');
        progressBar.className = 'percentage-gauge';

        const progressFill = document.createElement('div');
        progressFill.className = 'percentage-gauge-fill';
        progressFill.style.width = `${value}%`;

        // Añadir elementos al contenedor
        progressBar.appendChild(progressFill);
        gaugeContainer.appendChild(valueTitle);
        gaugeContainer.appendChild(valueLabel);
        gaugeContainer.appendChild(progressBar);

        container.innerHTML = '';
        container.appendChild(gaugeContainer);
    }

    /**
     * Renderiza un widget de gráfica
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderChartWidget(container, widget) {
        const chartType = widget.params.chartType;
        if (!chartType) {
            console.error('Error: No se especificó el tipo de gráfica');
            return;
        }

        // Obtener información del tipo de dato principal
        const valueTypeInfo = dataService.getValueTypes().find(type => type.id === chartType);
        const unit = valueTypeInfo ? valueTypeInfo.unit : '';

        // Obtener el número de puntos a mostrar (por defecto 10)
        const maxPoints = widget.params.chartPoints || 10;

        // Obtener el histórico de datos para la serie principal
        const history = dataService.getDataHistory(chartType, maxPoints);

        // Inicializar el array de series si no existe
        if (!widget.params.series) {
            widget.params.series = [];
            console.log('Array de series inicializado en renderChartWidget para widget ID:', widget.id);

            // Guardar el widget actualizado para persistir el array de series
            this.updateWidget(widget.id, {
                params: {
                    ...widget.params
                }
            });
        }

        // Depurar datos históricos
        console.log(`Renderizando gráfica para ${chartType}:`, history);

        // Formatear etiquetas de tiempo (usamos las de la serie principal)
        const labels = history.timestamps.map(timestamp => {
            const date = new Date(timestamp);
            return date.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        });

        // Crear o actualizar el contenedor del canvas
        let chartContainer = container.querySelector('.chart-container');
        if (!chartContainer) {
            console.log('Creando nuevo contenedor de gráfica');
            chartContainer = document.createElement('div');
            chartContainer.className = 'chart-container';
            chartContainer.style.width = '100%';
            chartContainer.style.height = '100%';
            container.innerHTML = '';
            container.appendChild(chartContainer);

            const canvas = document.createElement('canvas');
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            chartContainer.appendChild(canvas);

            // Comprobar si Chart.js está disponible
            if (typeof Chart === 'undefined') {
                console.error('Error: Chart.js no está cargado correctamente');
                return;
            }

            try {
                // Obtener el tipo de gráfica a mostrar (por defecto 'line')
                const chartDisplayType = widget.params.chartDisplayType || 'line';

                // Preparar los datasets para todas las series
                const datasets = [];

                // Configurar dataset para la serie principal
                let mainDatasetConfig = {
                    label: valueTypeInfo ? valueTypeInfo.name : chartType,
                    data: history.values,
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderWidth: 2
                };

                // Añadir propiedades específicas según el tipo de gráfica
                if (chartDisplayType === 'line') {
                    mainDatasetConfig.tension = 0.3;
                    mainDatasetConfig.pointRadius = 3;
                } else if (chartDisplayType === 'bar') {
                    // Para gráficas de barras no necesitamos tension ni pointRadius
                    delete mainDatasetConfig.tension;
                    delete mainDatasetConfig.pointRadius;
                } else if (chartDisplayType === 'pie' || chartDisplayType === 'doughnut' || chartDisplayType === 'polarArea') {
                    // Para gráficas circulares necesitamos un array de colores de fondo
                    delete mainDatasetConfig.tension;
                    delete mainDatasetConfig.pointRadius;
                    delete mainDatasetConfig.borderColor;

                    // Generar colores para cada punto de datos
                    const backgroundColors = history.values.map((_, index) => {
                        const hue = (index * 137) % 360; // Distribución de colores usando número áureo
                        return `hsla(${hue}, 70%, 60%, 0.7)`;
                    });
                    mainDatasetConfig.backgroundColor = backgroundColors;
                }

                // Añadir la serie principal
                datasets.push(mainDatasetConfig);

                // Añadir series adicionales si existen
                if (widget.params.series && widget.params.series.length > 0) {
                    widget.params.series.forEach((serie, index) => {
                        // Obtener datos para esta serie
                        const serieHistory = dataService.getDataHistory(serie.type, maxPoints);
                        if (!serieHistory || !serieHistory.values || serieHistory.values.length === 0) {
                            return; // Saltar si no hay datos
                        }

                        // Obtener información del tipo de dato
                        const serieTypeInfo = dataService.getValueTypes().find(type => type.id === serie.type);

                        // Generar un color aleatorio para esta serie
                        const hue = ((index + 1) * 137) % 360; // Usar número áureo para distribución de colores
                        const borderColor = `hsl(${hue}, 70%, 50%)`;
                        const backgroundColor = `hsla(${hue}, 70%, 50%, 0.2)`;

                        // Configurar dataset para esta serie
                        const serieDatasetConfig = {
                            label: serieTypeInfo ? serieTypeInfo.name : serie.type,
                            data: serieHistory.values,
                            borderColor: borderColor,
                            backgroundColor: backgroundColor,
                            borderWidth: 2
                        };

                        // Añadir propiedades específicas según el tipo de gráfica
                        if (chartDisplayType === 'line') {
                            serieDatasetConfig.tension = 0.3;
                            serieDatasetConfig.pointRadius = 3;
                        }

                        // Añadir esta serie al array de datasets
                        datasets.push(serieDatasetConfig);
                    });
                }

                // Crear gráfica con Chart.js
                widget.chart = new Chart(canvas, {
                    type: chartDisplayType,
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: chartDisplayType === 'pie' || chartDisplayType === 'doughnut' || chartDisplayType === 'polarArea' ? {} // Para gráficas circulares no mostramos ejes
                            :
                            {
                                y: {
                                    beginAtZero: false,
                                    title: {
                                        display: true,
                                        text: unit
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: 'Tiempo'
                                    }
                                }
                            },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                enabled: true
                            },
                            title: {
                                display: false
                            }
                        },
                        animation: {
                            duration: 500
                        }
                    }
                });
                console.log('Gráfica creada correctamente:', widget.chart);
            } catch (error) {
                console.error('Error al crear la gráfica:', error);
            }
        } else {
            // Actualizar datos de la gráfica existente
            try {
                // Verificar si el objeto chart existe y es válido
                if (widget.chart && typeof widget.chart.update === 'function') {
                    console.log('Actualizando gráfica existente con nuevos datos:', history);

                    // Actualizar etiquetas
                    widget.chart.data.labels = labels;

                    // Actualizar datos de la serie principal
                    widget.chart.data.datasets[0].data = history.values;

                    // Si es un tipo de gráfica circular, actualizar los colores de fondo
                    const chartDisplayType = widget.params.chartDisplayType || 'line';
                    if (chartDisplayType === 'pie' || chartDisplayType === 'doughnut' || chartDisplayType === 'polarArea') {
                        // Generar colores para cada punto de datos
                        const backgroundColors = history.values.map((_, index) => {
                            const hue = (index * 137) % 360; // Distribución de colores usando número áureo
                            return `hsla(${hue}, 70%, 60%, 0.7)`;
                        });
                        widget.chart.data.datasets[0].backgroundColor = backgroundColors;
                    }

                    // Actualizar series adicionales
                    if (widget.params.series && widget.params.series.length > 0) {
                        // Asegurarse de que hay suficientes datasets en la gráfica
                        while (widget.chart.data.datasets.length < widget.params.series.length + 1) {
                            // Crear un nuevo dataset vacío
                            widget.chart.data.datasets.push({
                                data: [],
                                borderColor: '#000000',
                                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                                borderWidth: 2
                            });
                        }

                        // Actualizar cada serie
                        widget.params.series.forEach((serie, index) => {
                            // Obtener datos para esta serie
                            const serieHistory = dataService.getDataHistory(serie.type, maxPoints);
                            if (!serieHistory || !serieHistory.values || serieHistory.values.length === 0) {
                                return; // Saltar si no hay datos
                            }

                            // Obtener información del tipo de dato
                            const serieTypeInfo = dataService.getValueTypes().find(type => type.id === serie.type);

                            // Actualizar el dataset correspondiente
                            const datasetIndex = index + 1; // +1 porque el índice 0 es la serie principal

                            // Actualizar datos y etiqueta
                            widget.chart.data.datasets[datasetIndex].data = serieHistory.values;
                            widget.chart.data.datasets[datasetIndex].label = serieTypeInfo ? serieTypeInfo.name : serie.type;
                        });

                        // Eliminar datasets sobrantes si se han quitado series
                        if (widget.chart.data.datasets.length > widget.params.series.length + 1) {
                            widget.chart.data.datasets = widget.chart.data.datasets.slice(0, widget.params.series.length + 1);
                        }
                    } else {
                        // Si no hay series adicionales, asegurarse de que solo queda el dataset principal
                        if (widget.chart.data.datasets.length > 1) {
                            widget.chart.data.datasets = [widget.chart.data.datasets[0]];
                        }
                    }

                    // Actualizar sin animación para evitar consumo excesivo de recursos
                    widget.chart.update('none');
                } else {
                    // El objeto chart no existe o no es válido, recrearlo
                    console.warn('No se encontró objeto chart válido para actualizar, recreando...');

                    // Limpiar cualquier objeto chart existente para evitar memory leaks
                    if (widget.chart) {
                        try {
                            widget.chart.destroy();
                        } catch (e) {
                            console.error('Error al destruir el objeto chart:', e);
                        }
                        widget.chart = null;
                    }

                    // Recrear el contenedor y la gráfica
                    container.innerHTML = '';
                    this.renderChartWidget(container, widget);
                }
            } catch (error) {
                console.error('Error al actualizar la gráfica, recreando:', error);

                // Limpiar cualquier objeto chart existente para evitar memory leaks
                if (widget.chart) {
                    try {
                        widget.chart.destroy();
                    } catch (e) {
                        console.error('Error al destruir el objeto chart:', e);
                    }
                    widget.chart = null;
                }

                // Recrear el contenedor y la gráfica
                container.innerHTML = '';
                this.renderChartWidget(container, widget);
            }
        }

        // Aplicar color de texto personalizado si existe
        try {
            if (widget.chart && typeof widget.chart.update === 'function') {
                if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                    // Aplicar color personalizado
                    if (widget.chart.options.plugins.title) {
                        widget.chart.options.plugins.title.color = widget.style.textColor;
                    }
                    widget.chart.options.scales.x.ticks.color = widget.style.textColor;
                    widget.chart.options.scales.y.ticks.color = widget.style.textColor;
                    widget.chart.update('none');
                } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
                    // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
                    if (widget.chart.options.plugins.title) {
                        widget.chart.options.plugins.title.color = dashboardManager.dashboard.widgetTextColor;
                    }
                    widget.chart.options.scales.x.ticks.color = dashboardManager.dashboard.widgetTextColor;
                    widget.chart.options.scales.y.ticks.color = dashboardManager.dashboard.widgetTextColor;
                    widget.chart.update('none');
                }
            }
        } catch (error) {
            console.error('Error al aplicar color de texto a la gráfica:', error);
            // No recreamos la gráfica aquí, ya que el color de texto no es crítico
            // y podría causar un bucle infinito de recreación
        }
    }

    /**
     * Hace que un widget sea arrastrable
     * @param {HTMLElement} widgetElement - Elemento del widget
     */
    makeWidgetDraggable(widgetElement) {
        let isDragging = false;
        let initialMouseX, initialMouseY;
        let initialLeft, initialTop;
        let dashboardOffset = {
            x: 0,
            y: 0
        };

        // Variables para arrastre múltiple
        let isMultipleDrag = false;
        let selectedWidgetElements = [];
        let selectedWidgetData = [];
        let initialPositions = [];

        // Variables para detección de arrastre
        let dragStarted = false;
        let dragThreshold = 5; // píxeles mínimos para considerar que es un arrastre

        widgetElement.addEventListener('mousedown', (e) => {
            const dashboard = app.dashboardManager.dashboardElement;
            if (!dashboard) return;

            // No iniciar arrastre si se hace clic en el manejador de redimensionamiento
            if (e.target.classList.contains('resize-handle') || e.target.closest('.resize-handle')) {
                return;
            }

            // No iniciar arrastre si se hace clic en un checkbox de selección
            if (e.target.type === 'checkbox' || e.target.closest('.widget-checkbox-container')) {
                return;
            }

            // Resetear variables de arrastre
            dragStarted = false;

            // Prevenir selección de texto durante el arrastre
            e.preventDefault();

            // Obtener el ID del widget
            const widgetId = parseInt(widgetElement.dataset.widgetId);

            // Obtener el widget de la estructura de datos
            const widget = this.getWidgets().find(w => w.id === widgetId);

            if (!widget) {
                console.error("Widget no encontrado en la estructura de datos:", widgetId);
                return;
            }

            // Verificar si estamos en modo selección y hay widgets seleccionados
            if (app.dashboardSelectionManager.enModoSeleccion()) {
                const selectedCheckboxes = document.querySelectorAll('.widget-selector:checked');

                // Verificar si el widget actual está seleccionado
                const currentWidgetCheckbox = widgetElement.querySelector('.widget-selector');
                const isCurrentWidgetSelected = currentWidgetCheckbox && currentWidgetCheckbox.checked;

                if (isCurrentWidgetSelected && selectedCheckboxes.length > 1) {
                    // Activar modo arrastre múltiple
                    isMultipleDrag = true;
                    selectedWidgetElements = [];
                    selectedWidgetData = [];
                    initialPositions = [];

                    // Recopilar todos los widgets seleccionados
                    selectedCheckboxes.forEach(checkbox => {
                        const selectedWidget = checkbox.closest('.widget');
                        const selectedWidgetId = parseInt(selectedWidget.dataset.widgetId);
                        const selectedWidgetObj = this.getWidgets().find(w => w.id === selectedWidgetId);

                        if (selectedWidgetObj) {
                            selectedWidgetElements.push(selectedWidget);
                            selectedWidgetData.push(selectedWidgetObj);
                            // Guardar posición inicial de cada widget
                            initialPositions.push({
                                x: selectedWidgetObj.x,
                                y: selectedWidgetObj.y
                            });
                        }
                    });

                    console.log(`Iniciando arrastre múltiple de ${selectedWidgetData.length} widgets`);
                }
            }

            // Guardar la posición inicial del mouse
            initialMouseX = e.clientX;
            initialMouseY = e.clientY;

            // Obtener el offset del dashboard respecto a la ventana
            const dashboardRect = dashboard.getBoundingClientRect();
            dashboardOffset = {
                x: dashboardRect.left + window.scrollX,
                y: dashboardRect.top + window.scrollY
            };

            // Usar las coordenadas del objeto widget en lugar de los estilos CSS
            initialLeft = widget.x;
            initialTop = widget.y;

            console.log("Iniciando arrastre. Widget coords:", widget.x, widget.y, "Estilos:", widgetElement.style.left, widgetElement.style.top, "Dashboard offset:", dashboardOffset);

            isDragging = true;

            // Añadir clase para indicar que el widget está siendo arrastrado
            if (isMultipleDrag) {
                selectedWidgetElements.forEach(el => el.classList.add('dragging'));
            } else {
                widgetElement.classList.add('dragging');
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            // Calcular el desplazamiento del mouse desde el punto inicial
            const deltaX = e.clientX - initialMouseX;
            const deltaY = e.clientY - initialMouseY;

            // Si no hemos iniciado el arrastre, verificar si superamos el umbral
            if (!dragStarted) {
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                if (distance >= dragThreshold) {
                    // Activar el modo edición ahora que confirmamos que es un arrastre
                    const dashboard = app.dashboardManager.dashboardElement;
                    if (!dashboard.classList.contains('edit-mode')) {
                        app.dashboardManager.activateMoveMode();
                    }
                    dragStarted = true;
                } else {
                    // No hemos superado el umbral, no hacer nada
                    return;
                }
            }

            if (isMultipleDrag) {
                // Arrastre múltiple: mover todos los widgets seleccionados
                selectedWidgetData.forEach((widget, index) => {
                    const element = selectedWidgetElements[index];
                    const initialPos = initialPositions[index];

                    // Calcular nueva posición = posición inicial + delta
                    let newX = initialPos.x + deltaX;
                    let newY = initialPos.y + deltaY;

                    // NO aplicar límites del dashboard en arrastre múltiple
                    // Permitir que los widgets salgan fuera del tablero

                    // Actualizar posición del widget en el DOM
                    element.style.left = `${newX}px`;
                    element.style.top = `${newY}px`;

                    // Actualizar posición del widget en la estructura de datos
                    widget.x = newX;
                    widget.y = newY;
                });
            } else {
                // Arrastre individual: comportamiento original
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidgets().find(w => w.id === widgetId);

                if (!widget) {
                    console.error("Widget no encontrado en la estructura de datos:", widgetId);
                    return;
                }

                // Calcular la nueva posición basada en la posición inicial + el desplazamiento
                let newX = initialLeft + deltaX;
                let newY = initialTop + deltaY;

                const dashboard = app.dashboardManager.dashboardElement;

                // Limitar posición dentro del dashboard
                // Evitar que se salga por la izquierda o arriba
                if (newX < 0) newX = 0;
                if (newY < 0) newY = 0;

                // Evitar que se salga por la derecha o abajo
                const maxX = dashboard.offsetWidth - widgetElement.offsetWidth;
                const maxY = dashboard.offsetHeight - widgetElement.offsetHeight;

                if (newX > maxX) newX = maxX;
                if (newY > maxY) newY = maxY;

                // Actualizar posición del widget en el DOM
                widgetElement.style.left = `${newX}px`;
                widgetElement.style.top = `${newY}px`;

                // Actualizar posición del widget en la estructura de datos
                widget.x = newX;
                widget.y = newY;
            }
        });

        document.addEventListener('mouseup', () => {
            if (!isDragging) return;

            isDragging = false;

            if (isMultipleDrag) {
                // Quitar clase de arrastre de todos los widgets
                selectedWidgetElements.forEach(el => el.classList.remove('dragging'));

                // Verificar si hay widgets fuera del tablero
                this.handleMultipleDragEnd(selectedWidgetData, selectedWidgetElements, initialPositions);

                // Limpiar variables de arrastre múltiple
                isMultipleDrag = false;
                selectedWidgetElements = [];
                selectedWidgetData = [];
                initialPositions = [];
            } else {
                // Arrastre individual: comportamiento original
                widgetElement.classList.remove('dragging');

                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidgets().find(w => w.id === widgetId);

                if (!widget) {
                    console.error("Widget no encontrado en la estructura de datos:", widgetId);
                    return;
                }

                // Guardar los cambios
                this.saveWidgets(this.getWidgets());
                console.log("Widget posición guardada:", widget.x, widget.y);
            }

            //En modo selección, al acabar de mover el widget salir del modo edición pq sino en el móvil
            //los eventos como clic fuera de widgets no cierran popups abiertos
            if (app.dashboardSelectionManager.enModoSeleccion()) app.dashboardManager.deactivateEditMode();
        });
    }

    /**
     * Maneja el final del arrastre múltiple, verificando widgets fuera del tablero
     * @param {Array} selectedWidgetData - Array de objetos de datos de widgets
     * @param {Array} selectedWidgetElements - Array de elementos DOM de widgets
     * @param {Array} initialPositions - Array de posiciones iniciales
     */
    handleMultipleDragEnd(selectedWidgetData, selectedWidgetElements, initialPositions) {
        const dashboard = app.dashboardManager.dashboardElement;
        const dashboardWidth = dashboard.offsetWidth;
        const dashboardHeight = dashboard.offsetHeight;

        // Verificar qué widgets están fuera del tablero
        const widgetsOutside = [];
        selectedWidgetData.forEach((widget, index) => {
            const element = selectedWidgetElements[index];
            const isOutside = widget.x < 0 ||
                            widget.y < 0 ||
                            widget.x + element.offsetWidth > dashboardWidth ||
                            widget.y + element.offsetHeight > dashboardHeight;

            if (isOutside) {
                widgetsOutside.push({
                    widget: widget,
                    element: element,
                    index: index
                });
            }
        });

        if (widgetsOutside.length > 0) {
            // Mostrar diálogo de confirmación
            const message = `${widgetsOutside.length} widget(s) han quedado fuera del área visible del tablero.`;

            dialogManager.custom({
                title: 'Widgets fuera de vista',
                content: `<p>${message}</p>`,
                buttons: [
                    {
                        text: 'Reset',
                        type: 'secondary',
                        action: () => {
                            // Restaurar posiciones iniciales
                            selectedWidgetData.forEach((widget, index) => {
                                const element = selectedWidgetElements[index];
                                const initialPos = initialPositions[index];

                                widget.x = initialPos.x;
                                widget.y = initialPos.y;
                                element.style.left = `${initialPos.x}px`;
                                element.style.top = `${initialPos.y}px`;
                            });

                            this.saveWidgets(this.getWidgets());
                            showNotification('Widgets restaurados a su posición inicial', 'info');
                        }
                    },
                    {
                        text: 'Permitir',
                        type: 'primary',
                        action: () => {
                            // Guardar posiciones actuales (incluso fuera del tablero)
                            this.saveWidgets(this.getWidgets());
                            showNotification('Widgets guardados en su posición actual', 'info');
                        }
                    },
                    {
                        text: 'No permitir',
                        type: 'danger',
                        action: () => {
                            // Reposicionar widgets fuera del tablero cerca del borde más cercano
                            this.repositionWidgetsNearBorder(widgetsOutside, dashboardWidth, dashboardHeight);
                            this.saveWidgets(this.getWidgets());
                            showNotification('Widgets reposicionados dentro del tablero', 'info');
                        }
                    }
                ]
            });
        } else {
            // No hay widgets fuera, guardar normalmente
            this.saveWidgets(this.getWidgets());
            console.log("Arrastre múltiple completado, todas las posiciones guardadas");
        }
    }

    /**
     * Reposiciona widgets cerca del borde más cercano del tablero
     * @param {Array} widgetsOutside - Array de widgets fuera del tablero
     * @param {number} dashboardWidth - Ancho del tablero
     * @param {number} dashboardHeight - Alto del tablero
     */
    repositionWidgetsNearBorder(widgetsOutside, dashboardWidth, dashboardHeight) {
        widgetsOutside.forEach((item, index) => {
            const { widget, element } = item;
            const widgetWidth = element.offsetWidth;
            const widgetHeight = element.offsetHeight;

            let newX = widget.x;
            let newY = widget.y;

            // Determinar el borde más cercano y reposicionar
            if (widget.x < 0) {
                // Fuera por la izquierda
                newX = 10 + (index * 20); // Pequeña distancia aleatoria
            } else if (widget.x + widgetWidth > dashboardWidth) {
                // Fuera por la derecha
                newX = dashboardWidth - widgetWidth - 10 - (index * 20);
            }

            if (widget.y < 0) {
                // Fuera por arriba
                newY = 10 + (index * 20);
            } else if (widget.y + widgetHeight > dashboardHeight) {
                // Fuera por abajo
                newY = dashboardHeight - widgetHeight - 10 - (index * 20);
            }

            // Asegurar que no se salga por otros bordes después del ajuste
            newX = Math.max(0, Math.min(newX, dashboardWidth - widgetWidth));
            newY = Math.max(0, Math.min(newY, dashboardHeight - widgetHeight));

            // Actualizar posición
            widget.x = newX;
            widget.y = newY;
            element.style.left = `${newX}px`;
            element.style.top = `${newY}px`;
        });
    }

    /**
     * Hace que un widget sea redimensionable
     * @param {HTMLElement} widgetElement - Elemento del widget
     * @param {HTMLElement} resizeHandle - Manejador de redimensionamiento
     */
    makeWidgetResizable(widgetElement, resizeHandle) {
        let isResizing = false;
        let originalWidth, originalHeight, startX, startY;
        let dashboardOffset = {
            x: 0,
            y: 0
        };

        resizeHandle.addEventListener('mousedown', (e) => {
            // Solo permitir redimensionar si estamos en modo de edición
            const dashboard = app.dashboardManager.dashboardElement;
            if (!dashboard || !dashboard.classList.contains('edit-mode')) return;

            isResizing = true;
            originalWidth = widgetElement.offsetWidth;
            originalHeight = widgetElement.offsetHeight;
            startX = e.clientX;
            startY = e.clientY;

            // Obtener el offset del dashboard respecto a la ventana
            const dashboardRect = dashboard.getBoundingClientRect();
            dashboardOffset = {
                x: dashboardRect.left + window.scrollX,
                y: dashboardRect.top + window.scrollY
            };

            console.log("Iniciando redimensionamiento. Widget size:", originalWidth, originalHeight, "Dashboard offset:", dashboardOffset);

            // Prevenir selección de texto durante el redimensionamiento
            e.preventDefault();
            e.stopPropagation(); // Evitar que se active el modo de movimiento
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;

            // Calcular nuevas dimensiones
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            const newWidth = Math.max(50, originalWidth + deltaX); // Mínimo 50px de ancho
            const newHeight = Math.max(50, originalHeight + deltaY); // Mínimo 50px de alto

            // Obtener el desplazamiento del scroll (no usado actualmente pero podría ser útil en el futuro)
            // const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
            // const scrollY = window.pageYOffset || document.documentElement.scrollTop;

            console.log("Resize - New size:", newWidth, newHeight, "Dashboard offset:", dashboardOffset);

            // Actualizar dimensiones del widget
            widgetElement.style.width = `${newWidth}px`;
            widgetElement.style.height = `${newHeight}px`;
        });

        document.addEventListener('mouseup', () => {
            if (!isResizing) return;

            isResizing = false;

            // Guardar nuevas dimensiones
            const widgetId = parseInt(widgetElement.dataset.widgetId);
            const width = widgetElement.offsetWidth;
            const height = widgetElement.offsetHeight;

            // Actualizar widget en la configuración
            const widget = this.getWidgets().find(w => w.id === widgetId);
            if (widget) {
                widget.width = width;
                widget.height = height;
                this.saveWidgets(this.getWidgets());
            }
        });
    }

    /**
     * Genera la descripción de un arco SVG
     * @param {number} x - Coordenada X del centro
     * @param {number} y - Coordenada Y del centro
     * @param {number} radius - Radio del arco
     * @param {number} startAngle - Ángulo inicial en grados
     * @param {number} endAngle - Ángulo final en grados
     * @returns {string} - Descripción del arco
     */
    describeArc(x, y, radius, startAngle, endAngle) {
        const start = this.polarToCartesian(x, y, radius, endAngle);
        const end = this.polarToCartesian(x, y, radius, startAngle);

        const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

        return [
            "M", start.x, start.y,
            "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y
        ].join(" ");
    }

    /**
     * Convierte coordenadas polares a cartesianas
     * @param {number} centerX - Coordenada X del centro
     * @param {number} centerY - Coordenada Y del centro
     * @param {number} radius - Radio
     * @param {number} angleInDegrees - Ángulo en grados
     * @returns {Object} - Coordenadas cartesianas {x, y}
     */
    polarToCartesian(centerX, centerY, radius, angleInDegrees) {
        const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;

        return {
            x: centerX + (radius * Math.cos(angleInRadians)),
            y: centerY + (radius * Math.sin(angleInRadians))
        };
    }

    /**
     * Asegura que todos los widgets estén dentro de los límites del dashboard
     * Se llama cuando cambian las dimensiones del dashboard
     */
    ensureWidgetsAreWithinDashboard() {
        const dashboard = app.dashboardManager.dashboardElement;
        if (!dashboard) return;

        const dashboardWidth = dashboard.offsetWidth;
        const dashboardHeight = dashboard.offsetHeight;

        console.log("Asegurando que los widgets estén dentro del dashboard:", dashboardWidth, dashboardHeight);

        // Obtener todos los widgets
        const widgets = this.getWidgets();
        let widgetsUpdated = false;

        // Verificar cada widget
        widgets.forEach(widget => {
            let needsUpdate = false;

            // Verificar si el widget se sale por la derecha
            if (widget.x + widget.width > dashboardWidth) {
                widget.x = Math.max(0, dashboardWidth - widget.width);
                needsUpdate = true;
            }

            // Verificar si el widget se sale por abajo
            if (widget.y + widget.height > dashboardHeight) {
                widget.y = Math.max(0, dashboardHeight - widget.height);
                needsUpdate = true;
            }

            // Si el widget es más grande que el dashboard, ajustar su tamaño
            if (widget.width > dashboardWidth) {
                widget.width = dashboardWidth;
                needsUpdate = true;
            }

            if (widget.height > dashboardHeight) {
                widget.height = dashboardHeight;
                needsUpdate = true;
            }

            if (needsUpdate) {
                widgetsUpdated = true;
                console.log(`Widget ${widget.id} ajustado a: x=${widget.x}, y=${widget.y}, width=${widget.width}, height=${widget.height}`);
            }
        });

        // Si se actualizó algún widget, guardar los cambios
        if (widgetsUpdated) {
            this.saveWidgets(widgets);
        }
    }

    /**
     * Renderiza un widget de últimos valores
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderLatestWidget(container, widget) {
        const latestType = widget.params.latestType;
        if (!latestType) return;

        // Obtener información del tipo de dato
        const valueTypeInfo = dataService.getValueTypes().find(type => type.id === latestType);
        const unit = valueTypeInfo ? valueTypeInfo.unit : '';

        // Obtener el número de datos a mostrar (por defecto 5)
        const maxItems = widget.params.latestCount || 5;

        // Obtener configuración de visualización
        const includeUnit = widget.params.includeUnit !== undefined ? widget.params.includeUnit : true;
        const includeFullTime = widget.params.includeFullTime !== undefined ? widget.params.includeFullTime : false;
        const includeMinutesSeconds = widget.params.includeMinutesSeconds !== undefined ? widget.params.includeMinutesSeconds : false;

        // Obtener el histórico de datos
        const history = dataService.getDataHistory(latestType, maxItems);

        // Crear contenedor para la lista de valores
        const latestContainer = document.createElement('div');
        latestContainer.className = 'latest-container';
        latestContainer.style.height = '100%';
        latestContainer.style.display = 'flex';
        latestContainer.style.flexDirection = 'column';

        // Crear contenedor para los valores (sin título) con scroll
        const valuesContainer = document.createElement('div');
        valuesContainer.className = 'latest-values';
        valuesContainer.style.overflowY = 'auto';
        valuesContainer.style.maxHeight = '100%';
        valuesContainer.style.padding = '2px';

        // Calcular el número de columnas necesarias
        // Asumimos que cada elemento ocupa aproximadamente 30px de alto
        const containerHeight = widget.height; // No restar espacio para el título ya que no lo mostramos
        const itemsPerColumn = Math.max(1, Math.floor(containerHeight / 30));
        const numColumns = Math.ceil(maxItems / itemsPerColumn);

        // Configurar el grid para las columnas
        valuesContainer.style.display = 'grid';
        valuesContainer.style.gridTemplateColumns = `repeat(${numColumns}, 1fr)`;
        valuesContainer.style.gap = '5px';
        valuesContainer.style.width = '100%';

        // Crear elementos para cada valor
        if (history.values.length > 0) {
            // Invertir los arrays para mostrar los más recientes primero
            const reversedValues = [...history.values].reverse();
            const reversedTimestamps = [...history.timestamps].reverse();

            reversedValues.forEach((value, index) => {
                const valueElement = document.createElement('div');
                valueElement.className = 'latest-value-item';

                // Formatear la hora según la configuración
                const date = new Date(reversedTimestamps[index]);
                let timeStr = '';

                if (includeFullTime) {
                    // Incluir hora completa (hh:mm:ss)
                    timeStr = date.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                } else if (includeMinutesSeconds) {
                    // Incluir solo minutos y segundos (mm:ss)
                    timeStr = date.toLocaleTimeString([], {
                        minute: '2-digit',
                        second: '2-digit'
                    });
                }

                // Formatear el valor con o sin unidad según la configuración
                const formattedValue = includeUnit ? `${value} ${unit}` : `${value}`;

                // Construir el HTML del elemento
                if (timeStr) {
                    valueElement.innerHTML = `<span class="latest-time">${timeStr}</span>: <span class="latest-value">${formattedValue}</span>`;
                } else {
                    valueElement.innerHTML = `<span class="latest-value">${formattedValue}</span>`;
                }

                // Aplicar color de texto personalizado si existe
                if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                    valueElement.style.color = widget.style.textColor;
                } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
                    // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
                    valueElement.style.color = dashboardManager.dashboard.widgetTextColor;
                }

                valuesContainer.appendChild(valueElement);
            });
        } else {
            const noDataElement = document.createElement('div');
            noDataElement.className = 'latest-no-data';
            noDataElement.textContent = 'No hay datos disponibles';

            // Aplicar color de texto personalizado si existe
            if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                noDataElement.style.color = widget.style.textColor;
            } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
                // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
                noDataElement.style.color = dashboardManager.dashboard.widgetTextColor;
            }

            valuesContainer.appendChild(noDataElement);
        }

        // Añadir elementos al contenedor principal (solo el contenedor de valores, sin título)
        latestContainer.appendChild(valuesContainer);

        // Limpiar el contenedor y añadir el nuevo contenido
        container.innerHTML = '';
        container.appendChild(latestContainer);
    }

    /**
     * Renderiza un widget de gráfica por períodos
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderPeriodChartWidget(container, widget) {
        const periodChartType = widget.params.periodChartType;
        if (!periodChartType) {
            console.error('Error: No se especificó el tipo de gráfica por períodos');
            return;
        }

        // Obtener información del tipo de dato
        const valueTypeInfo = dataService.getValueTypes().find(type => type.id === periodChartType);
        const unit = valueTypeInfo ? valueTypeInfo.unit : '';

        // Obtener el número de períodos a mostrar (por defecto 10)
        const numPeriods = widget.params.periodChartPoints || 10;

        // Obtener el ancho del período en segundos (por defecto 15)
        const periodWidth = widget.params.periodChartWidth || 15;

        // Obtener el histórico de datos completo
        const history = dataService.getDataHistory(periodChartType, 50); // Obtener más datos para tener suficiente historial

        // Crear o actualizar el contenedor del canvas
        let chartContainer = container.querySelector('.chart-container');
        if (!chartContainer) {
            console.log('Creando nuevo contenedor de gráfica por períodos');
            chartContainer = document.createElement('div');
            chartContainer.className = 'chart-container';
            chartContainer.style.width = '100%';
            chartContainer.style.height = '100%';
            chartContainer.style.position = 'relative'; // Para posicionar el botón de datos
            container.innerHTML = '';
            container.appendChild(chartContainer);

            // Crear botón para mostrar datos
            const dataButton = document.createElement('button');
            dataButton.className = 'period-data-button';
            dataButton.innerHTML = '<i class="fas fa-table"></i>'; // Icono de tabla
            dataButton.title = 'Ver datos de períodos';
            dataButton.style.position = 'absolute';
            dataButton.style.top = '5px';
            dataButton.style.right = '5px';
            dataButton.style.zIndex = '10';
            dataButton.style.padding = '5px';
            dataButton.style.fontSize = '12px';
            dataButton.style.border = 'none';
            dataButton.style.borderRadius = '3px';
            dataButton.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
            dataButton.style.cursor = 'pointer';

            // Añadir evento de clic al botón
            dataButton.addEventListener('click', (e) => {
                // Mostrar popup con todos los datos de todos los períodos
                this.showAllPeriodsDataPopup(
                    widget.periodValueType,
                    widget.periodData,
                    widget.periodUnit,
                    widget.periodWidth
                );

                // Evitar que el evento se propague
                e.stopPropagation();
            });

            chartContainer.appendChild(dataButton);

            const canvas = document.createElement('canvas');
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            chartContainer.appendChild(canvas);

            // Comprobar si Chart.js está disponible
            if (typeof Chart === 'undefined') {
                console.error('Error: Chart.js no está cargado correctamente');
                return;
            }

            try {
                // Procesar los datos por períodos
                const periodData = this.calculatePeriodData(history, numPeriods, periodWidth);

                // Configurar datasets para mínimo, máximo y media
                const datasets = [{
                        label: 'Mínimo',
                        data: periodData.mins,
                        borderColor: 'rgba(54, 162, 235, 0.8)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'Media',
                        data: periodData.avgs,
                        borderColor: 'rgba(255, 159, 64, 0.8)',
                        backgroundColor: 'rgba(255, 159, 64, 0.2)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'Máximo',
                        data: periodData.maxs,
                        borderColor: 'rgba(255, 99, 132, 0.8)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderWidth: 2,
                        fill: false
                    }
                ];

                // Crear etiquetas para los períodos (números del 1 al numPeriods)
                const labels = Array.from({
                    length: numPeriods
                }, (_, i) => `P${i+1}`);

                // Crear gráfica con Chart.js
                widget.chart = new Chart(canvas, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                title: {
                                    display: true,
                                    text: unit
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: `Períodos (${periodWidth}s)`
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                enabled: true,
                                callbacks: {
                                    label: function (context) {
                                        const label = context.dataset.label || '';
                                        const value = context.parsed.y;
                                        return `${label}: ${value} ${unit}`;
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: valueTypeInfo ? valueTypeInfo.name : periodChartType
                            }
                        },
                        animation: {
                            duration: 500
                        },
                        onClick: () => {
                            // No procesar clics simples, solo doble clic
                            return;
                        }
                    }
                });

                // Guardar referencia a los datos del período y la unidad para usarlos en el popup
                widget.periodData = periodData;
                widget.periodUnit = unit;
                widget.periodWidth = periodWidth;
                widget.periodValueType = valueTypeInfo ? valueTypeInfo.name : periodChartType;

                // Configurar opciones de Chart.js para mostrar tooltip al hacer hover
                widget.chart.options.plugins.tooltip.enabled = true;
                widget.chart.options.plugins.tooltip.mode = 'nearest';
                widget.chart.options.plugins.tooltip.intersect = true;
                widget.chart.update('none');

                // Añadir evento de doble clic para mostrar detalles del punto
                canvas.addEventListener('dblclick', (e) => {
                    // Obtener el punto más cercano al clic
                    const points = widget.chart.getElementsAtEventForMode(e, 'nearest', {
                        intersect: true
                    }, false);

                    if (points.length > 0) {
                        // Hay un punto de datos cerca del clic
                        const firstPoint = points[0];
                        const index = firstPoint.index;

                        // Obtener los datos del punto
                        const periodLabel = widget.chart.data.labels[index];

                        // Obtener los valores min, max y avg para este período
                        const minValue = widget.periodData.mins[index];
                        const avgValue = widget.periodData.avgs[index];
                        const maxValue = widget.periodData.maxs[index];

                        // Obtener los valores y timestamps originales para este período
                        const periodValues = widget.periodData.periodValues[index] || [];
                        const periodTimestamps = widget.periodData.periodTimestamps[index] || [];

                        // Mostrar popup con los detalles
                        this.showPeriodDataPopup(
                            widget.periodValueType,
                            periodLabel,
                            minValue,
                            avgValue,
                            maxValue,
                            widget.periodUnit,
                            widget.periodWidth,
                            e.clientX,
                            e.clientY,
                            periodValues,
                            periodTimestamps
                        );

                        // Detener la propagación del evento para evitar que se active el evento de doble clic del widget
                        e.stopPropagation();
                    }
                    // Si no hay punto de datos cerca del clic, permitir que el evento se propague
                    // para que se active el evento de doble clic del widget (editar widget)
                });
                console.log('Gráfica por períodos creada correctamente:', widget.chart);
            } catch (error) {
                console.error('Error al crear la gráfica por períodos:', error);
            }
        } else {
            // Actualizar datos de la gráfica existente
            if (widget.chart) {
                try {
                    console.log('Actualizando gráfica por períodos existente con nuevos datos');

                    // Procesar los datos por períodos
                    const periodData = this.calculatePeriodData(history, numPeriods, periodWidth);

                    // Actualizar datos
                    widget.chart.data.datasets[0].data = periodData.mins;
                    widget.chart.data.datasets[1].data = periodData.avgs;
                    widget.chart.data.datasets[2].data = periodData.maxs;

                    // Actualizar referencia a los datos del período para el popup
                    widget.periodData = periodData;

                    // Actualizar sin animación para evitar consumo excesivo de recursos
                    widget.chart.update('none');
                } catch (error) {
                    console.error('Error al actualizar la gráfica por períodos:', error);
                }
            } else {
                console.warn('No se encontró objeto chart para actualizar');
                // Recrear el contenedor y la gráfica
                container.innerHTML = '';
                this.renderPeriodChartWidget(container, widget);
            }
        }

        // Aplicar color de texto personalizado si existe
        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto" && widget.chart) {
            try {
                if (widget.chart.options.plugins.title) {
                    widget.chart.options.plugins.title.color = widget.style.textColor;
                }
                widget.chart.options.scales.x.ticks.color = widget.style.textColor;
                widget.chart.options.scales.y.ticks.color = widget.style.textColor;
                widget.chart.update('none');
            } catch (error) {
                console.error('Error al aplicar color de texto personalizado a la gráfica por períodos:', error);
            }
        } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor && widget.chart) {
            try {
                // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
                if (widget.chart.options.plugins.title) {
                    widget.chart.options.plugins.title.color = dashboardManager.dashboard.widgetTextColor;
                }
                widget.chart.options.scales.x.ticks.color = dashboardManager.dashboard.widgetTextColor;
                widget.chart.options.scales.y.ticks.color = dashboardManager.dashboard.widgetTextColor;
                widget.chart.update('none');
            } catch (error) {
                console.error('Error al aplicar color de texto del tablero a la gráfica por períodos:', error);
            }
        }
    }

    /**
     * Calcula los datos por períodos (mínimo, máximo y media)
     * @param {Object} history - Histórico de datos
     * @param {number} numPeriods - Número de períodos a calcular
     * @param {number} periodWidth - Ancho del período en segundos
     * @returns {Object} - Objeto con arrays de mínimos, máximos, medias y datos originales por período
     */
    calculatePeriodData(history, numPeriods, periodWidth) {
        const result = {
            mins: [],
            maxs: [],
            avgs: [],
            // Añadir arrays para almacenar los datos originales de cada período
            periodValues: [], // Valores originales en cada período
            periodTimestamps: [] // Timestamps originales en cada período
        };

        if (!history || !history.values || history.values.length === 0) {
            // Si no hay datos, devolver arrays vacíos
            result.mins = Array(numPeriods).fill(0);
            result.maxs = Array(numPeriods).fill(0);
            result.avgs = Array(numPeriods).fill(0);
            result.periodValues = Array(numPeriods).fill([]);
            result.periodTimestamps = Array(numPeriods).fill([]);
            return result;
        }

        // Obtener el timestamp actual
        const now = new Date().getTime();

        // Calcular el ancho del período en milisegundos
        const periodWidthMs = periodWidth * 1000;

        // Para cada período, calcular mínimo, máximo y media
        for (let i = 0; i < numPeriods; i++) {
            // Calcular el rango de tiempo para este período
            const endTime = now - (i * periodWidthMs);
            const startTime = endTime - periodWidthMs;

            // Filtrar los valores que caen dentro de este período
            const periodValues = [];
            const periodTimestamps = [];

            for (let j = 0; j < history.timestamps.length; j++) {
                if (history.timestamps[j] >= startTime && history.timestamps[j] < endTime) {
                    periodValues.push(history.values[j]);
                    periodTimestamps.push(history.timestamps[j]);
                }
            }

            // Calcular mínimo, máximo y media para este período
            if (periodValues.length > 0) {
                const min = Math.min(...periodValues);
                const max = Math.max(...periodValues);
                const avg = periodValues.reduce((sum, val) => sum + val, 0) / periodValues.length;

                // Añadir los valores al resultado (en orden inverso para que el período más reciente esté a la derecha)
                result.mins.unshift(min);
                result.maxs.unshift(max);
                result.avgs.unshift(avg);

                // Guardar los valores y timestamps originales para este período
                result.periodValues.unshift([...periodValues]);
                result.periodTimestamps.unshift([...periodTimestamps]);
            } else {
                // Si no hay valores en este período, usar null o el último valor conocido
                const lastKnownValue = result.avgs.length > 0 ? result.avgs[0] : 0;
                result.mins.unshift(lastKnownValue);
                result.maxs.unshift(lastKnownValue);
                result.avgs.unshift(lastKnownValue);

                // Para los valores originales, usar arrays vacíos
                result.periodValues.unshift([]);
                result.periodTimestamps.unshift([]);
            }
        }

        return result;
    }

    /**
     * Renderiza un widget de últimos por períodos
     * @param {HTMLElement} container - Contenedor del widget
     * @param {Object} widget - Configuración del widget
     */
    renderLatestByPeriodsWidget(container, widget) {
        const latestByPeriodsType = widget.params.latestByPeriodsType;
        if (!latestByPeriodsType) return;

        // Obtener información del tipo de dato
        const valueTypeInfo = dataService.getValueTypes().find(type => type.id === latestByPeriodsType);
        const unit = valueTypeInfo ? valueTypeInfo.unit : '';

        // Obtener el número de períodos a mostrar (por defecto 10)
        const numPeriods = widget.params.latestByPeriodsPoints || 10;

        // Obtener el ancho del período en segundos (por defecto 15)
        const periodWidth = widget.params.latestByPeriodsWidth || 15;

        // Obtener configuración de visualización
        const includeUnit = widget.params.includeUnit !== undefined ? widget.params.includeUnit : true;

        // Inicializar el estado de ordenación si no existe
        if (!widget.sortState) {
            widget.sortState = {
                sortType: 'period', // Ordenar por período por defecto
                sortDirection: 'asc' // Orden ascendente por defecto
            };
        }

        // Obtener el histórico de datos completo
        const history = dataService.getDataHistory(latestByPeriodsType, 50); // Obtener más datos para tener suficiente historial

        // Procesar los datos por períodos
        const periodData = this.calculatePeriodData(history, numPeriods, periodWidth);

        // Crear contenedor para la lista de valores
        const latestContainer = document.createElement('div');
        latestContainer.className = 'latest-container';
        latestContainer.style.height = '100%';
        latestContainer.style.display = 'flex';
        latestContainer.style.flexDirection = 'column';

        // Crear título con el nombre del tipo de dato y la información del período
        const titleElement = document.createElement('div');
        titleElement.className = 'latest-by-periods-title';
        const typeName = valueTypeInfo ? valueTypeInfo.name : latestByPeriodsType;
        titleElement.innerHTML = `${typeName} <span style="font-size: 0.9em; opacity: 0.8;">(Períodos de ${periodWidth}s)</span>`;
        titleElement.style.flexShrink = '0'; // Evitar que el título se encoja

        // Aplicar color de texto personalizado al título si existe
        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
            titleElement.style.color = widget.style.textColor;
        } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
            // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
            titleElement.style.color = dashboardManager.dashboard.widgetTextColor;
        }

        latestContainer.appendChild(titleElement);

        // Crear contenedor con scroll para la tabla
        const tableContainer = document.createElement('div');
        tableContainer.style.overflowY = 'auto';
        tableContainer.style.maxHeight = 'calc(100% - 30px)'; // Dejar espacio para el título
        tableContainer.style.flexGrow = '1'; // Tomar el espacio restante

        // Crear tabla para mostrar los datos
        const tableElement = document.createElement('table');
        tableElement.className = 'latest-by-periods-table';
        tableElement.style.width = '100%';

        // Crear encabezado de la tabla
        const theadElement = document.createElement('thead');
        const headerRow = document.createElement('tr');

        // Columna de período (ahora solo muestra el número)
        const periodHeader = document.createElement('th');
        periodHeader.textContent = 'Período';
        periodHeader.style.cursor = 'pointer';
        periodHeader.dataset.sortType = 'period';
        periodHeader.dataset.sortDirection = widget.sortState.sortType === 'period' ? widget.sortState.sortDirection : 'asc';
        periodHeader.title = 'Ordenar por número de período';
        // Añadir indicador de ordenación si esta columna está activa
        if (widget.sortState.sortType === 'period') {
            periodHeader.textContent += widget.sortState.sortDirection === 'asc' ? ' ▲' : ' ▼';
        }
        headerRow.appendChild(periodHeader);

        // Columnas de datos
        const minHeader = document.createElement('th');
        minHeader.textContent = 'Mínimo';
        minHeader.style.cursor = 'pointer';
        minHeader.dataset.sortType = 'min';
        minHeader.dataset.sortDirection = widget.sortState.sortType === 'min' ? widget.sortState.sortDirection : 'asc';
        minHeader.title = 'Ordenar por valor mínimo';
        // Añadir indicador de ordenación si esta columna está activa
        if (widget.sortState.sortType === 'min') {
            minHeader.textContent += widget.sortState.sortDirection === 'asc' ? ' ▲' : ' ▼';
        }
        headerRow.appendChild(minHeader);

        const avgHeader = document.createElement('th');
        avgHeader.textContent = 'Media';
        avgHeader.style.cursor = 'pointer';
        avgHeader.dataset.sortType = 'avg';
        avgHeader.dataset.sortDirection = widget.sortState.sortType === 'avg' ? widget.sortState.sortDirection : 'asc';
        avgHeader.title = 'Ordenar por valor medio';
        // Añadir indicador de ordenación si esta columna está activa
        if (widget.sortState.sortType === 'avg') {
            avgHeader.textContent += widget.sortState.sortDirection === 'asc' ? ' ▲' : ' ▼';
        }
        headerRow.appendChild(avgHeader);

        const maxHeader = document.createElement('th');
        maxHeader.textContent = 'Máximo';
        maxHeader.style.cursor = 'pointer';
        maxHeader.dataset.sortType = 'max';
        maxHeader.dataset.sortDirection = widget.sortState.sortType === 'max' ? widget.sortState.sortDirection : 'asc';
        maxHeader.title = 'Ordenar por valor máximo';
        // Añadir indicador de ordenación si esta columna está activa
        if (widget.sortState.sortType === 'max') {
            maxHeader.textContent += widget.sortState.sortDirection === 'asc' ? ' ▲' : ' ▼';
        }
        headerRow.appendChild(maxHeader);

        theadElement.appendChild(headerRow);
        tableElement.appendChild(theadElement);

        // Crear cuerpo de la tabla
        const tbodyElement = document.createElement('tbody');

        // Preparar datos para ordenación
        const rowsData = [];
        for (let i = 0; i < numPeriods; i++) {
            rowsData.push({
                period: i + 1,
                min: periodData.mins[i],
                avg: periodData.avgs[i],
                max: periodData.maxs[i]
            });
        }

        // Función para crear las filas de la tabla
        const createTableRows = (data) => {
            // Limpiar el cuerpo de la tabla
            tbodyElement.innerHTML = '';

            // Crear filas para cada período
            data.forEach(rowData => {
                const row = document.createElement('tr');

                // Celda de período
                const periodCell = document.createElement('td');
                periodCell.textContent = `P${rowData.period}`;
                periodCell.dataset.value = rowData.period; // Para ordenación
                row.appendChild(periodCell);

                // Celdas de datos
                const minCell = document.createElement('td');
                minCell.textContent = includeUnit ? `${rowData.min.toFixed(2)} ${unit}` : rowData.min.toFixed(2);
                minCell.dataset.value = rowData.min; // Para ordenación
                row.appendChild(minCell);

                const avgCell = document.createElement('td');
                avgCell.textContent = includeUnit ? `${rowData.avg.toFixed(2)} ${unit}` : rowData.avg.toFixed(2);
                avgCell.dataset.value = rowData.avg; // Para ordenación
                row.appendChild(avgCell);

                const maxCell = document.createElement('td');
                maxCell.textContent = includeUnit ? `${rowData.max.toFixed(2)} ${unit}` : rowData.max.toFixed(2);
                maxCell.dataset.value = rowData.max; // Para ordenación
                row.appendChild(maxCell);

                // Aplicar color de texto personalizado a las celdas si existe
                if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                    periodCell.style.color = widget.style.textColor;
                    minCell.style.color = widget.style.textColor;
                    avgCell.style.color = widget.style.textColor;
                    maxCell.style.color = widget.style.textColor;
                } else if (widget.style && widget.style.textColor === "defecto" && dashboardManager.dashboard.widgetTextColor) {
                    // Si es "defecto" y hay un color de texto de widgets definido en el tablero, usarlo
                    periodCell.style.color = dashboardManager.dashboard.widgetTextColor;
                    minCell.style.color = dashboardManager.dashboard.widgetTextColor;
                    avgCell.style.color = dashboardManager.dashboard.widgetTextColor;
                    maxCell.style.color = dashboardManager.dashboard.widgetTextColor;
                }

                tbodyElement.appendChild(row);
            });
        };

        // Función para ordenar la tabla
        const sortTable = (sortType, direction) => {
            // Ordenar los datos
            rowsData.sort((a, b) => {
                const valueA = a[sortType];
                const valueB = b[sortType];

                if (direction === 'asc') {
                    return valueA - valueB;
                } else {
                    return valueB - valueA;
                }
            });

            // Recrear las filas con los datos ordenados
            createTableRows(rowsData);

            // Actualizar indicadores visuales de ordenación
            const headers = theadElement.querySelectorAll('th');
            headers.forEach(header => {
                // Quitar indicadores de todas las columnas
                header.textContent = header.textContent.replace(' ▲', '').replace(' ▼', '');

                // Añadir indicador a la columna activa
                if (header.dataset.sortType === sortType) {
                    header.textContent += direction === 'asc' ? ' ▲' : ' ▼';
                }
            });

            // Guardar el estado de ordenación en el widget para futuras renderizaciones
            widget.sortState = {
                sortType: sortType,
                sortDirection: direction
            };
        };

        // Ordenar los datos inicialmente según el estado guardado
        sortTable(widget.sortState.sortType, widget.sortState.sortDirection);

        // Añadir eventos de clic a los encabezados para ordenar
        const headers = theadElement.querySelectorAll('th');
        headers.forEach(header => {
            header.addEventListener('click', () => {
                const sortType = header.dataset.sortType;
                let direction;

                // Si hacemos clic en la misma columna que ya está ordenada, invertir la dirección
                if (sortType === widget.sortState.sortType) {
                    direction = widget.sortState.sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    // Si es una columna diferente, empezar con orden ascendente
                    direction = 'asc';
                }

                // Actualizar dirección en el dataset
                header.dataset.sortDirection = direction;

                // Ordenar la tabla
                sortTable(sortType, direction);
            });
        });

        tableElement.appendChild(tbodyElement);

        // Añadir la tabla al contenedor con scroll
        tableContainer.appendChild(tableElement);

        // Añadir el contenedor con scroll al contenedor principal
        latestContainer.appendChild(tableContainer);

        // Limpiar el contenedor y añadir el nuevo contenido
        container.innerHTML = '';
        container.appendChild(latestContainer);
    }

    /**
     * Muestra un popup con los datos detallados de un período
     * @param {string} valueType - Tipo de valor (temperatura, presión, etc.)
     * @param {string} periodLabel - Etiqueta del período (P1, P2, etc.)
     * @param {number} minValue - Valor mínimo del período
     * @param {number} avgValue - Valor medio del período
     * @param {number} maxValue - Valor máximo del período
     * @param {string} unit - Unidad de medida
     * @param {number} periodWidth - Ancho del período en segundos
     * @param {number} x - Posición X del clic
     * @param {number} y - Posición Y del clic
     * @param {Array} periodValues - Valores originales del período
     * @param {Array} periodTimestamps - Timestamps originales del período
     */
    showPeriodDataPopup(valueType, periodLabel, minValue, avgValue, maxValue, unit, periodWidth, x, y, periodValues = [], periodTimestamps = []) {
        // Formatear los valores con 2 decimales
        const formattedMin = minValue.toFixed(2);
        const formattedAvg = avgValue.toFixed(2);
        const formattedMax = maxValue.toFixed(2);

        // Crear el contenido HTML para el diálogo
        let contentHTML = `
            <p><strong>Período:</strong> ${periodWidth} segundos</p>
            <table class="period-data-table">
                <tr>
                    <th>Mínimo</th>
                    <th>Media</th>
                    <th>Máximo</th>
                </tr>
                <tr>
                    <td>${formattedMin} ${unit}</td>
                    <td>${formattedAvg} ${unit}</td>
                    <td>${formattedMax} ${unit}</td>
                </tr>
            </table>
        `;

        // Si hay valores individuales en el período, mostrarlos en una tabla
        if (periodValues && periodValues.length > 0) {
            contentHTML += `<h4 style="margin-top: 15px; margin-bottom: 5px;">Puntos de datos:</h4>`;
            contentHTML += `<div style="max-height: 200px; overflow-y: auto;">`;
            contentHTML += `<table class="period-points-table">
                <thead>
                    <tr>
                        <th>Hora</th>
                        <th>Valor</th>
                    </tr>
                </thead>
                <tbody>`;

            // Ordenar los datos por timestamp (más reciente primero)
            const sortedData = [];
            for (let i = 0; i < periodValues.length; i++) {
                sortedData.push({
                    timestamp: periodTimestamps[i],
                    value: periodValues[i]
                });
            }
            sortedData.sort((a, b) => b.timestamp - a.timestamp);

            // Crear filas para cada punto de datos
            for (const point of sortedData) {
                // Formatear timestamp
                const date = new Date(point.timestamp);
                const timeStr = date.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                // Formatear valor
                const valueStr = point.value.toFixed(2);

                contentHTML += `
                    <tr>
                        <td>${timeStr}</td>
                        <td>${valueStr} ${unit}</td>
                    </tr>
                `;
            }

            contentHTML += `</tbody></table></div>`;
        } else {
            // Si no hay puntos de datos, mostrar un mensaje
            contentHTML += `<p style="margin-top: 15px; font-style: italic;">No hay puntos de datos individuales en este período.</p>`;
        }

        // Usar el sistema de diálogos para mostrar el popup
        const dialogOptions = {
            title: `${valueType} - ${periodLabel}`,
            content: contentHTML,
            buttons: [{
                text: 'Cerrar',
                type: 'primary'
            }]
        };

        // Crear y mostrar el diálogo personalizado
        const dialog = dialogManager.custom(dialogOptions);

        // Posicionar el diálogo cerca del punto de clic
        // Esperar un momento para que el diálogo se renderice completamente
        setTimeout(() => {
            const dialogContent = document.querySelector('.custom-dialog .dialog-content');
            if (dialogContent) {
                // Calcular posición para que el diálogo no se salga de la ventana
                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;
                const dialogWidth = dialogContent.offsetWidth;
                const dialogHeight = dialogContent.offsetHeight;

                // Calcular posición inicial cerca del punto de clic
                let dialogX = x + 10; // 10px a la derecha del clic
                let dialogY = y + 10; // 10px debajo del clic

                // Ajustar si se sale por la derecha
                if (dialogX + dialogWidth > windowWidth) {
                    dialogX = x - dialogWidth - 10; // Colocar a la izquierda del clic
                }

                // Ajustar si se sale por abajo
                if (dialogY + dialogHeight > windowHeight) {
                    dialogY = y - dialogHeight - 10; // Colocar encima del clic
                }

                // Asegurarse de que no se salga por la izquierda o arriba
                dialogX = Math.max(10, dialogX);
                dialogY = Math.max(10, dialogY);

                // Aplicar la posición
                dialogContent.style.transform = 'none'; // Quitar la transformación centrada
                dialogContent.style.top = `${dialogY}px`;
                dialogContent.style.left = `${dialogX}px`;
            }
        }, 50);

        return dialog;
    }

    /**
     * Muestra un popup con los datos de todos los períodos
     * @param {string} valueType - Tipo de valor (temperatura, presión, etc.)
     * @param {Object} periodData - Datos de todos los períodos
     * @param {string} unit - Unidad de medida
     * @param {number} periodWidth - Ancho del período en segundos
     */
    showAllPeriodsDataPopup(valueType, periodData, unit, periodWidth) {
            // Crear el contenido HTML para el diálogo con pestañas
            let contentHTML = `
            <p><strong>Ancho de período:</strong> ${periodWidth} segundos</p>

            <div class="popup-tabs">
                <button class="popup-tab active" data-tab="summary">Resumen</button>
                <button class="popup-tab" data-tab="details">Detalles</button>
            </div>

            <div class="popup-tab-content">
                <!-- Pestaña de resumen -->
                <div class="tab-pane active" data-tab="summary">
                    <div style="max-height: 300px; overflow-y: auto;">
                        <table class="period-data-table">
                            <thead>
                                <tr>
                                    <th>Período</th>
                                    <th>Mínimo</th>
                                    <th>Media</th>
                                    <th>Máximo</th>
                                    <th>Puntos</th>
                                </tr>
                            </thead>
                            <tbody id="summary-table-body">
        `;

            // Añadir filas para cada período
            for (let i = 0; i < periodData.mins.length; i++) {
                // Formatear valores
                const minValue = periodData.mins[i].toFixed(2);
                const avgValue = periodData.avgs[i].toFixed(2);
                const maxValue = periodData.maxs[i].toFixed(2);
                const numPoints = periodData.periodValues[i].length;

                contentHTML += `
                <tr class="period-row" data-period="${i}" style="cursor: pointer;" title="Haz clic para ver los puntos de este período">
                    <td>P${i+1}</td>
                    <td>${minValue} ${unit}</td>
                    <td>${avgValue} ${unit}</td>
                    <td>${maxValue} ${unit}</td>
                    <td>${numPoints}</td>
                </tr>
            `;
            }

            contentHTML += `
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pestaña de detalles -->
                <div class="tab-pane" data-tab="details">
                    <div class="period-selector">
                        <label for="period-select">Seleccionar período: </label>
                        <select id="period-select">
                            ${Array.from({length: periodData.mins.length}, (_, i) => ` < option value = "${i}" > P$ {
                i + 1
            } < /option>`).join('')} < /
            select > <
                /div> <
            div id = "period-details-container"
            class = "period-details-container"
            style = "max-height: 300px; overflow-y: auto; margin-top: 10px;" >
                <
                !--Aquí se mostrarán los detalles del período seleccionado-- >
                <
                /div> < /
            div > <
                /div>
            `;

        // Usar el sistema de diálogos para mostrar el popup
        const dialogOptions = {
            title: `
            $ {
                valueType
            } - Todos los períodos `,
            content: contentHTML,
            buttons: [
                {
                    text: 'Cerrar',
                    type: 'primary'
                }
            ]
        };

        // Crear y mostrar el diálogo personalizado
        const dialog = dialogManager.custom(dialogOptions);

        // Configurar eventos después de que el diálogo se haya renderizado
        setTimeout(() => {
            // Obtener elementos del DOM
            const tabButtons = document.querySelectorAll('.popup-tab');
            const tabPanes = document.querySelectorAll('.tab-pane');
            const periodRows = document.querySelectorAll('.period-row');
            const periodSelect = document.getElementById('period-select');
            const detailsContainer = document.getElementById('period-details-container');

            // Función para mostrar los detalles de un período específico
            const showPeriodDetails = (periodIndex) => {
                // Actualizar el selector
                if (periodSelect) {
                    periodSelect.value = periodIndex;
                }

                // Obtener los datos del período
                const periodValues = periodData.periodValues[periodIndex] || [];
                const periodTimestamps = periodData.periodTimestamps[periodIndex] || [];

                // Limpiar el contenedor
                if (detailsContainer) {
                    detailsContainer.innerHTML = '';

                    if (periodValues.length === 0) {
                        detailsContainer.innerHTML = '<p class="no-data-message">No hay puntos de datos en este período.</p>';
                        return;
                    }

                    // Crear tabla para los puntos
                    let tableHTML = ` <
                table class = "period-points-table" >
                <
                thead >
                <
                tr >
                <
                th > Hora < /th> <
            th > Valor < /th> < /
            tr > <
                /thead> <
            tbody >
                `;

                    // Ordenar los datos por timestamp (más reciente primero)
                    const sortedData = [];
                    for (let i = 0; i < periodValues.length; i++) {
                        sortedData.push({
                            timestamp: periodTimestamps[i],
                            value: periodValues[i]
                        });
                    }
                    sortedData.sort((a, b) => b.timestamp - a.timestamp);

                    // Crear filas para cada punto
                    for (const point of sortedData) {
                        // Formatear timestamp
                        const date = new Date(point.timestamp);
                        const timeStr = date.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });

                        // Formatear valor
                        const valueStr = point.value.toFixed(2);

                        tableHTML += ` <
                tr >
                <
                td > $ {
                    timeStr
                } < /td> <
            td > $ {
                valueStr
            }
            $ {
                unit
            } < /td> < /
            tr >
                `;
                    }

                    tableHTML += ` <
                /tbody> < /
            table >
                `;

                    detailsContainer.innerHTML = tableHTML;
                }
            };

            // Configurar eventos de las pestañas
            tabButtons.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Desactivar todas las pestañas
                    tabButtons.forEach(t => t.classList.remove('active'));

                    // Activar la pestaña actual
                    tab.classList.add('active');

                    // Ocultar todos los contenidos de pestañas
                    tabPanes.forEach(pane => pane.classList.remove('active'));

                    // Mostrar el contenido de la pestaña actual
                    const tabName = tab.dataset.tab;
                    document.querySelector(`.tab - pane[data - tab = "${tabName}"]
            `).classList.add('active');
                });
            });

            // Configurar eventos de las filas de períodos
            periodRows.forEach(row => {
                row.addEventListener('click', () => {
                    const periodIndex = parseInt(row.dataset.period);

                    // Cambiar a la pestaña de detalles
                    tabButtons.forEach(tab => tab.classList.remove('active'));
                    document.querySelector('.popup-tab[data-tab="details"]').classList.add('active');

                    // Ocultar todas las pestañas y mostrar la de detalles
                    tabPanes.forEach(pane => pane.classList.remove('active'));
                    document.querySelector('.tab-pane[data-tab="details"]').classList.add('active');

                    // Mostrar solo los puntos de este período
                    showPeriodDetails(periodIndex);
                });
            });

            // Configurar evento de cambio en el selector de período
            if (periodSelect) {
                periodSelect.addEventListener('change', (e) => {
                    const periodIndex = parseInt(e.target.value);
                    showPeriodDetails(periodIndex);
                });

                // Mostrar detalles del primer período por defecto
                showPeriodDetails(0);
            }
        }, 100);

        return dialog;
    }

    /**
     * Actualiza los widgets de tipo valor, gauge, gauge porcentual y últimos con los datos más recientes
     * Los widgets de texto no se actualizan ya que no dependen de datos dinámicos
     */
    updateValueWidgets() {
        // Actualizar widgets de valor
        const valueWidgets = document.querySelectorAll('.value-widget');
        if (valueWidgets.length > 0) {
            valueWidgets.forEach(widgetElement => {
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidget(widgetId);

                if (widget && widget.type === 'value') {
                    // Guardar el color actual antes de actualizar
                    const currentColor = widgetElement.style.color;

                    const contentElement = widgetElement.querySelector('.widget-content');
                    if (contentElement) {
                        this.renderValueWidget(contentElement, widget);

                        // Restaurar el color si era personalizado
                        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                            widgetElement.style.color = widget.style.textColor;
                            contentElement.style.color = widget.style.textColor;
                        } else if (currentColor) {
                            // Si no tenía un color personalizado pero sí tenía un color aplicado, restaurarlo
                            widgetElement.style.color = currentColor;
                        }
                    }
                }
            });
        }

        // Actualizar widgets de gauge
        const gaugeWidgets = document.querySelectorAll('.gauge-widget');
        if (gaugeWidgets.length > 0) {
            gaugeWidgets.forEach(widgetElement => {
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidget(widgetId);

                if (widget && widget.type === 'gauge') {
                    // Guardar el color actual antes de actualizar
                    const currentColor = widgetElement.style.color;

                    const contentElement = widgetElement.querySelector('.widget-content');
                    if (contentElement) {
                        this.renderGaugeWidget(contentElement, widget);

                        // Restaurar el color si era personalizado
                        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                            widgetElement.style.color = widget.style.textColor;
                            contentElement.style.color = widget.style.textColor;
                        } else if (currentColor) {
                            // Si no tenía un color personalizado pero sí tenía un color aplicado, restaurarlo
                            widgetElement.style.color = currentColor;
                        }
                    }
                }
            });
        }

        // Actualizar widgets de gauge porcentual
        const percentageGaugeWidgets = document.querySelectorAll('.percentage-gauge-widget');
        if (percentageGaugeWidgets.length > 0) {
            percentageGaugeWidgets.forEach(widgetElement => {
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidget(widgetId);

                if (widget && widget.type === 'percentage-gauge') {
                    // Guardar el color actual antes de actualizar
                    const currentColor = widgetElement.style.color;

                    const contentElement = widgetElement.querySelector('.widget-content');
                    if (contentElement) {
                        this.renderPercentageGaugeWidget(contentElement, widget);

                        // Restaurar el color si era personalizado
                        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                            widgetElement.style.color = widget.style.textColor;
                            contentElement.style.color = widget.style.textColor;
                        } else if (currentColor) {
                            // Si no tenía un color personalizado pero sí tenía un color aplicado, restaurarlo
                            widgetElement.style.color = currentColor;
                        }
                    }
                }
            });
        }

        // Actualizar widgets de últimos valores
        const latestWidgets = document.querySelectorAll('.latest-widget');
        if (latestWidgets.length > 0) {
            latestWidgets.forEach(widgetElement => {
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidget(widgetId);

                if (widget && widget.type === 'latest') {
                    // Guardar el color actual antes de actualizar
                    const currentColor = widgetElement.style.color;

                    const contentElement = widgetElement.querySelector('.widget-content');
                    if (contentElement) {
                        this.renderLatestWidget(contentElement, widget);

                        // Restaurar el color si era personalizado
                        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                            widgetElement.style.color = widget.style.textColor;
                            contentElement.style.color = widget.style.textColor;
                        } else if (currentColor) {
                            // Si no tenía un color personalizado pero sí tenía un color aplicado, restaurarlo
                            widgetElement.style.color = currentColor;
                        }
                    }
                }
            });
        }

        // Actualizar widgets de gráfica por períodos
        const periodChartWidgets = document.querySelectorAll('.period-chart-widget');
        if (periodChartWidgets.length > 0) {
            periodChartWidgets.forEach(widgetElement => {
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidget(widgetId);

                if (widget && widget.type === 'period-chart') {
                    // Guardar el color actual antes de actualizar
                    const currentColor = widgetElement.style.color;

                    const contentElement = widgetElement.querySelector('.widget-content');
                    if (contentElement) {
                        this.renderPeriodChartWidget(contentElement, widget);

                        // Restaurar el color si era personalizado
                        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                            widgetElement.style.color = widget.style.textColor;
                            contentElement.style.color = widget.style.textColor;
                        } else if (currentColor) {
                            // Si no tenía un color personalizado pero sí tenía un color aplicado, restaurarlo
                            widgetElement.style.color = currentColor;
                        }
                    }
                }
            });
        }

        // Actualizar widgets de últimos por períodos
        const latestByPeriodsWidgets = document.querySelectorAll('.latest-by-periods-widget');
        if (latestByPeriodsWidgets.length > 0) {
            latestByPeriodsWidgets.forEach(widgetElement => {
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidget(widgetId);

                if (widget && widget.type === 'latest-by-periods') {
                    // Guardar el color actual antes de actualizar
                    const currentColor = widgetElement.style.color;

                    const contentElement = widgetElement.querySelector('.widget-content');
                    if (contentElement) {
                        this.renderLatestByPeriodsWidget(contentElement, widget);

                        // Restaurar el color si era personalizado
                        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                            widgetElement.style.color = widget.style.textColor;
                            contentElement.style.color = widget.style.textColor;
                        } else if (currentColor) {
                            // Si no tenía un color personalizado pero sí tenía un color aplicado, restaurarlo
                            widgetElement.style.color = currentColor;
                        }
                    }
                }
            });
        }

        // Restaurar colores de texto para widgets de texto (sin re-renderizar)
        const textWidgets = document.querySelectorAll('.text-widget');
        if (textWidgets.length > 0) {
            textWidgets.forEach(widgetElement => {
                const widgetId = parseInt(widgetElement.dataset.widgetId);
                const widget = this.getWidget(widgetId);

                if (widget && widget.type === 'text') {
                    const contentElement = widgetElement.querySelector('.widget-content');
                    if (contentElement) {
                        // No re-renderizar, solo restaurar el color y asegurar que el texto sea correcto
                        if (widget.style && widget.style.textColor && widget.style.textColor !== "defecto") {
                            widgetElement.style.color = widget.style.textColor;
                            contentElement.style.color = widget.style.textColor;
                            // Forzar el color con !important para evitar que sea sobrescrito
                            widgetElement.setAttribute('style', widgetElement.getAttribute('style') + '; color: ' + widget.style.textColor + ' !important;');
                        }

                        // Asegurar que el texto sea correcto
                        const textContent = widget.params.text || '';
                        if (contentElement.textContent !== textContent) {
                            contentElement.textContent = textContent;
                        }
                    }
                }
            });
        }
    }
}

// Crear instancia del gestor de widgets
const widgetManager = new WidgetManager();